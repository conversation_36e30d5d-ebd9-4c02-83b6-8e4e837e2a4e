import type { OfflineMutationRecord } from "@/hooks/useOfflineMutation"

import { useNetworkStatusStore } from "@/stores/networkStatusStore"
import { ConnectionQualityEnum, connectionQualitySchema } from "@/types/api"

import { MutationOutboxManager } from "@/hooks/useOfflineMutation"

// Re-export ConnectionQuality for convenience
export { connectionQualitySchema as ConnectionQuality }

/**
 * SyncManager - Offline mutation synchronization service
 *
 * This service monitors network status changes and automatically processes
 * offline mutations from IndexedDB when the application comes back online.
 *
 * Features:
 * - Automatic network status monitoring
 * - Offline mutation queue processing
 * - Retry logic with exponential backoff
 * - Conflict resolution for failed syncs
 * - Progress tracking and event notifications
 * - Error resilient with comprehensive logging
 */

/**
 * Sync operation status
 */
export enum SyncStatus {
  IDLE = "idle",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

/**
 * Sync result for individual mutations
 */
export interface MutationSyncResult {
  /** Original mutation record */
  mutation: OfflineMutationRecord
  /** Sync operation status */
  status: SyncStatus
  /** Success response data if successful */
  data?: any
  /** Error information if failed */
  error?: {
    message: string
    code?: string
    retryable?: boolean
  }
  /** Number of retry attempts made */
  retryAttempts: number
  /** Time taken for sync operation */
  duration: number
}

/**
 * Overall sync operation result
 */
export interface SyncOperationResult {
  /** Operation status */
  status: SyncStatus
  /** Total mutations processed */
  totalMutations: number
  /** Successfully synced mutations */
  successCount: number
  /** Failed mutations */
  failedCount: number
  /** Mutations that were skipped */
  skippedCount: number
  /** Individual mutation results */
  results: MutationSyncResult[]
  /** Total operation duration */
  duration: number
  /** Timestamp when operation started */
  startTime: number
  /** Timestamp when operation completed */
  endTime?: number
}

/**
 * Sync manager configuration
 */
export interface SyncManagerConfig {
  /** Maximum number of retry attempts per mutation */
  maxRetries: number
  /** Base retry delay in milliseconds */
  retryBaseDelay: number
  /** Maximum retry delay in milliseconds */
  maxRetryDelay: number
  /** Maximum concurrent sync operations */
  maxConcurrentSyncs: number
  /** Minimum connection quality required for sync */
  minConnectionQuality: ConnectionQualityEnum
  /** Enable detailed logging */
  enableLogging: boolean
  /** Custom fetch function for API calls */
  fetchFn?: typeof fetch
  /** Custom base URL for API endpoints */
  baseUrl?: string
}

/**
 * Default sync manager configuration
 */
const DEFAULT_CONFIG: SyncManagerConfig = {
  maxRetries: 3,
  retryBaseDelay: 1000,
  maxRetryDelay: 30000,
  maxConcurrentSyncs: 3,
  minConnectionQuality: connectionQualitySchema.enum.Moderate,
  enableLogging: true,
}

/**
 * Event types for sync manager notifications
 */
export type SyncEventType =
  | "sync-started"
  | "sync-progress"
  | "sync-completed"
  | "sync-failed"
  | "mutation-synced"
  | "mutation-failed"

/**
 * Sync event data
 */
export interface SyncEvent {
  type: SyncEventType
  timestamp: number
  data: any
}

/**
 * SyncManager class for handling offline mutation synchronization
 */
export class SyncManager {
  private config: SyncManagerConfig
  private outboxManager: MutationOutboxManager
  private isProcessing: boolean = false
  private currentSyncOperation: SyncOperationResult | null = null
  private eventListeners: Map<SyncEventType, Set<(event: SyncEvent) => void>> =
    new Map()
  private networkStatusUnsubscribe: (() => void) | null = null

  constructor(config: Partial<SyncManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.outboxManager = new MutationOutboxManager()

    this._initializeNetworkMonitoring()

    if (this.config.enableLogging) {
      console.log("[SyncManager] Initialized with config:", this.config)
    }
  }

  /**
   * Initialize network status monitoring
   */
  private _initializeNetworkMonitoring(): void {
    if (typeof window === "undefined") {
      return // Skip in SSR
    }

    // Subscribe to online/offline changes
    this.networkStatusUnsubscribe = useNetworkStatusStore.subscribe(
      (state) => state.connection.isOnline,
      (isOnline, previousIsOnline) => {
        if (this.config.enableLogging) {
          console.log(
            `[SyncManager] Network status changed: ${isOnline ? "ONLINE" : "OFFLINE"}`
          )
        }

        // Process outbox when coming online
        if (isOnline && !previousIsOnline) {
          this._handleOnlineTransition()
        }

        // Cancel processing when going offline
        if (!isOnline && previousIsOnline) {
          this._handleOfflineTransition()
        }
      }
    )
  }

  /**
   * Handle transition from offline to online
   */
  private async _handleOnlineTransition(): Promise<void> {
    if (this.config.enableLogging) {
      console.log("[SyncManager] Handling online transition - checking outbox")
    }

    // Wait a moment for connection to stabilize
    await this._delay(1000)

    // Check if connection quality is sufficient
    const networkState = useNetworkStatusStore.getState()
    if (!this._isConnectionSufficient(networkState.connection.quality)) {
      if (this.config.enableLogging) {
        console.log(
          "[SyncManager] Connection quality insufficient for sync:",
          networkState.connection.quality
        )
      }
      return
    }

    // Process outbox if not already processing
    if (!this.isProcessing) {
      await this.processOutbox()
    }
  }

  /**
   * Handle transition from online to offline
   */
  private async _handleOfflineTransition(): Promise<void> {
    if (this.config.enableLogging) {
      console.log("[SyncManager] Handling offline transition")
    }

    // Currently we don't cancel ongoing syncs immediately
    // They will fail naturally and be retried when online
  }

  /**
   * Check if connection quality is sufficient for sync operations
   */
  private _isConnectionSufficient(quality: ConnectionQualityEnum): boolean {
    const qualityOrder = {
      [connectionQualitySchema.enum.Offline]: 0,
      // Unknown quality is treated as Slow to err on the side of caution
      [connectionQualitySchema.enum.Slow]: 1,
      [connectionQualitySchema.enum.Moderate]: 3,
      [connectionQualitySchema.enum.Fast]: 4,
    }

    return (
      qualityOrder[quality] >= qualityOrder[this.config.minConnectionQuality]
    )
  }

  /**
   * Process all pending mutations in the outbox
   */
  public async processOutbox(): Promise<SyncOperationResult> {
    if (this.isProcessing) {
      throw new Error("Sync operation already in progress")
    }

    this.isProcessing = true
    const startTime = Date.now()

    const operation: SyncOperationResult = {
      status: SyncStatus.PROCESSING,
      totalMutations: 0,
      successCount: 0,
      failedCount: 0,
      skippedCount: 0,
      results: [],
      duration: 0,
      startTime,
    }

    this.currentSyncOperation = operation
    this._emitEvent("sync-started", { operation })

    try {
      // Get all pending mutations
      const pendingMutations = await this.outboxManager.getPendingMutations()
      operation.totalMutations = pendingMutations.length

      if (this.config.enableLogging) {
        console.log(
          `[SyncManager] Processing ${pendingMutations.length} pending mutations`
        )
      }

      if (pendingMutations.length === 0) {
        operation.status = SyncStatus.COMPLETED
        operation.endTime = Date.now()
        operation.duration = operation.endTime - startTime

        this._emitEvent("sync-completed", { operation })
        return operation
      }

      // Process mutations with concurrency control
      const batches = this._createBatches(
        pendingMutations,
        this.config.maxConcurrentSyncs
      )

      for (const batch of batches) {
        const batchPromises = batch.map((mutation) =>
          this._syncMutation(mutation)
        )
        const batchResults = await Promise.all(batchPromises)

        operation.results.push(...batchResults)

        // Update counters
        for (const result of batchResults) {
          switch (result.status) {
            case SyncStatus.COMPLETED:
              operation.successCount++
              break
            case SyncStatus.FAILED:
              operation.failedCount++
              break
            default:
              operation.skippedCount++
          }
        }

        // Emit progress event
        this._emitEvent("sync-progress", {
          processed: operation.results.length,
          total: operation.totalMutations,
          success: operation.successCount,
          failed: operation.failedCount,
          skipped: operation.skippedCount,
        })

        // Break if we should stop processing
        if (!this.isProcessing) {
          operation.status = SyncStatus.CANCELLED
          break
        }
      }

      // Determine final status
      if (operation.status === SyncStatus.PROCESSING) {
        operation.status =
          operation.failedCount > 0
            ? operation.successCount > 0
              ? SyncStatus.COMPLETED
              : SyncStatus.FAILED
            : SyncStatus.COMPLETED
      }

      operation.endTime = Date.now()
      operation.duration = operation.endTime - startTime

      if (this.config.enableLogging) {
        console.log("[SyncManager] Sync operation completed:", {
          status: operation.status,
          total: operation.totalMutations,
          success: operation.successCount,
          failed: operation.failedCount,
          duration: operation.duration,
        })
      }

      const eventType =
        operation.status === SyncStatus.FAILED
          ? "sync-failed"
          : "sync-completed"
      this._emitEvent(eventType, { operation })

      return operation
    } catch (error) {
      operation.status = SyncStatus.FAILED
      operation.endTime = Date.now()
      operation.duration = operation.endTime - startTime

      if (this.config.enableLogging) {
        console.error("[SyncManager] Sync operation failed:", error)
      }

      this._emitEvent("sync-failed", { operation, error })
      throw error
    } finally {
      this.isProcessing = false
      this.currentSyncOperation = null
    }
  }

  /**
   * Sync an individual mutation
   */
  private async _syncMutation(
    mutation: OfflineMutationRecord
  ): Promise<MutationSyncResult> {
    const startTime = Date.now()
    const retryAttempts = mutation.retryCount || 0

    const result: MutationSyncResult = {
      mutation,
      status: SyncStatus.PROCESSING,
      retryAttempts,
      duration: 0,
    }

    try {
      // Construct API request
      const url = this.config.baseUrl
        ? `${this.config.baseUrl}${mutation.endpoint}`
        : mutation.endpoint

      const fetchOptions: RequestInit = {
        method: mutation.method,
        headers: {
          "Content-Type": "application/json",
        },
      }

      // Add body for non-GET requests
      if (mutation.method !== "GET" && mutation.payload) {
        fetchOptions.body = JSON.stringify(mutation.payload)
      }

      // Execute request with retry logic
      const response = await this._executeWithRetry(
        url,
        fetchOptions,
        retryAttempts
      )

      if (response.ok) {
        const data = await response.json().catch(() => ({}))

        result.status = SyncStatus.COMPLETED
        result.data = data

        // Remove from outbox on success
        if (mutation.id) {
          await this.outboxManager.removeMutation(mutation.id)
        }

        if (this.config.enableLogging) {
          console.log(
            `[SyncManager] Successfully synced mutation ${mutation.id}`
          )
        }

        this._emitEvent("mutation-synced", { mutation, result })
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      result.status = SyncStatus.FAILED
      result.error = {
        message: error instanceof Error ? error.message : "Unknown error",
        retryable: this._isRetryableError(error),
      }

      if (this.config.enableLogging) {
        console.error(
          `[SyncManager] Failed to sync mutation ${mutation.id}:`,
          error
        )
      }

      this._emitEvent("mutation-failed", { mutation, result, error })

      // Update retry count in outbox if retryable
      if (
        result.error.retryable &&
        retryAttempts < this.config.maxRetries &&
        mutation.id
      ) {
        try {
          // Note: In a real implementation, you'd update the mutation record in IndexedDB
          // For now, we'll just log that we would retry
          if (this.config.enableLogging) {
            console.log(
              `[SyncManager] Will retry mutation ${mutation.id} (attempt ${retryAttempts + 1})`
            )
          }
        } catch (updateError) {
          console.error(
            "[SyncManager] Failed to update retry count:",
            updateError
          )
        }
      }
    }

    result.duration = Date.now() - startTime
    return result
  }

  /**
   * Execute HTTP request with retry logic
   */
  private async _executeWithRetry(
    url: string,
    options: RequestInit,
    _initialRetryCount: number = 0
  ): Promise<Response> {
    let lastError: Error | null = null
    const fetchFn = this.config.fetchFn || fetch

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        const response = await fetchFn(url, options)

        // Return successful responses immediately
        if (response.ok) {
          return response
        }

        // For non-2xx responses, throw an error to trigger retry logic
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error")

        // Don't retry if this is the last attempt or error is not retryable
        if (
          attempt === this.config.maxRetries ||
          !this._isRetryableError(error)
        ) {
          break
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.config.retryBaseDelay * Math.pow(2, attempt),
          this.config.maxRetryDelay
        )

        if (this.config.enableLogging) {
          console.log(
            `[SyncManager] Retrying request after ${delay}ms (attempt ${attempt + 1}/${this.config.maxRetries})`
          )
        }

        await this._delay(delay)
      }
    }

    throw lastError
  }

  /**
   * Check if an error is retryable
   */
  private _isRetryableError(error: unknown): boolean {
    if (error instanceof TypeError && error.message.includes("fetch")) {
      return true // Network errors are retryable
    }

    if (error instanceof Error) {
      const message = error.message.toLowerCase()

      // Retryable HTTP errors
      if (
        message.includes("http 5") || // 5xx server errors
        message.includes("timeout") ||
        message.includes("network") ||
        message.includes("connection")
      ) {
        return true
      }

      // Non-retryable errors (4xx client errors)
      if (message.includes("http 4")) {
        return false
      }
    }

    return true // Default to retryable for unknown errors
  }

  /**
   * Create batches for concurrent processing
   */
  private _createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []

    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }

    return batches
  }

  /**
   * Utility delay function
   */
  private _delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Emit sync event to listeners
   */
  private _emitEvent(type: SyncEventType, data: any): void {
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      const event: SyncEvent = {
        type,
        timestamp: Date.now(),
        data,
      }
      listeners.forEach((listener) => {
        try {
          listener(event)
        } catch (error) {
          console.error("[SyncManager] Event listener error:", error)
        }
      })
    }
  }

  /**
   * Add event listener
   */
  public addEventListener(
    type: SyncEventType,
    listener: (event: SyncEvent) => void
  ): () => void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set())
    }

    this.eventListeners.get(type)!.add(listener)

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(type)?.delete(listener)
    }
  }

  /**
   * Get current sync operation status
   */
  public getCurrentOperation(): SyncOperationResult | null {
    return this.currentSyncOperation
  }

  /**
   * Check if sync manager is currently processing
   */
  public isCurrentlyProcessing(): boolean {
    return this.isProcessing
  }

  /**
   * Get sync manager statistics
   */
  public async getStats() {
    const outboxStats = await this.outboxManager.getStats()
    const networkState = useNetworkStatusStore.getState()

    return {
      outbox: outboxStats,
      network: {
        isOnline: networkState.connection.isOnline,
        quality: networkState.connection.quality,
        isStable: networkState.isConnectionStable(),
      },
      sync: {
        isProcessing: this.isProcessing,
        currentOperation: this.currentSyncOperation,
      },
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    if (this.networkStatusUnsubscribe) {
      this.networkStatusUnsubscribe()
      this.networkStatusUnsubscribe = null
    }

    this.eventListeners.clear()

    if (this.config.enableLogging) {
      console.log("[SyncManager] Destroyed")
    }
  }
}

/**
 * Global sync manager instance
 */
let globalSyncManager: SyncManager | null = null

/**
 * Get or create global sync manager instance
 */
export function getSyncManager(
  config?: Partial<SyncManagerConfig>
): SyncManager {
  if (!globalSyncManager) {
    globalSyncManager = new SyncManager(config)
  }
  return globalSyncManager
}

/**
 * Initialize sync manager with network monitoring
 * Call this once in your app root
 */
export function initializeSyncManager(
  config?: Partial<SyncManagerConfig>
): SyncManager {
  return getSyncManager(config)
}
