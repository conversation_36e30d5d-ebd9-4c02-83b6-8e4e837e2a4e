/**
 * Core caching functionality for client-side persistence
 *
 * This module provides IndexedDB-based caching capabilities for React Query,
 * enabling offline functionality and improved user experience.
 */

export {
  IndexedDBPersister,
  createIndexedDBPersister,
  isIndexedDBSupported,
  type IndexedDBPersisterConfig,
} from "./indexed_db_persister"

export { CacheProvider, CacheManager, useQueryClient } from "./cache_provider"
export type { CacheStats } from "./indexed_db_persister"
