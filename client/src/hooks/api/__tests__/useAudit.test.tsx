/**
 * Unit tests for useAudit hooks
 */

import { ReactNode } from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { auditApiClient } from "@/lib/api/audit"

import {
  useActivityLogs,
  useAuditSummary,
  useAuditTrails,
  useCreateActivityLog,
  useCreateAuditTrail,
  useLogDataChange,
  useLogSecurityEvent,
  useLogUserActivity,
  useRecordHistory,
  useUpdateActivityLog,
  useUserActivitySummary,
} from "../useAudit"

enum ErrorSeverityEnum {
  Info = "Info",
  Low = "Low",
  Medium = "MEDIUM",
  High = "HIGH",
  Critical = "CRITICAL",
}

// Mock QueryKeys to ensure proper test execution
vi.mock("@/types/api", async () => {
  const actual = await vi.importActual("@/types/api")
  return {
    ...actual,
    QueryKeys: {
      ...(actual as any).QueryKeys,
      audit: {
        activityLogsList: vi.fn((params) => ["activity-logs", "list", params]),
        activityLog: vi.fn((id) => ["activity-logs", id]),
        auditTrailsList: vi.fn((params) => ["audit-trails", "list", params]),
        auditTrail: vi.fn((id) => ["audit-trails", id]),
        recordHistory: vi.fn((tableName, recordId) => [
          "audit-trails",
          "record-history",
          tableName,
          recordId,
        ]),
        userActivitySummary: vi.fn((userId) => [
          "users",
          userId,
          "activity-summary",
        ]),
        auditSummary: vi.fn((params) => ["audit-trails", "summary", params]),
        activityLogs: ["activity-logs"] as const,
        auditTrails: ["audit-trails"] as const,
        userActivityLogs: vi.fn((userId, params) => [
          "users",
          userId,
          "activity-logs",
          params,
        ]),
        securityEvents: vi.fn((params) => [
          "activity-logs",
          "security",
          params,
        ]),
      },
    },
  }
})

// Mock the auditApiClient
vi.mock("@/lib/api/audit", () => ({
  auditApiClient: {
    getActivityLogs: vi.fn(),
    createActivityLog: vi.fn(),
    updateActivityLog: vi.fn(),
    getAuditTrails: vi.fn(),
    createAuditTrail: vi.fn(),
    getRecordHistory: vi.fn(),
    logUserActivity: vi.fn(),
    logSecurityEvent: vi.fn(),
    logDataChange: vi.fn(),
    getAuditSummary: vi.fn(),
    getUserActivitySummary: vi.fn(),
  },
}))

// Mock data
const mockActivityLogs = {
  items: [
    {
      id: 1,
      name: "LOGIN_2023_01_01",
      user_id: 1,
      session_id: "session_123",
      action_type: "LOGIN",
      action_description: "User logged in successfully",
      target_type: "User",
      target_id: 1,
      target_name: "john.doe",
      request_method: "POST",
      request_path: "/api/v1/auth/login",
      request_ip: "***********",
      user_agent: "Mozilla/5.0",
      status: "SUCCESS",
      severity: "Info" as ErrorSeverityEnum,
      metadata: { login_method: "password" },
      error_message: undefined,
      execution_time_ms: 150,
      category: "AUTHENTICATION",
      tags: ["auth", "login"],
      is_security_related: true,
      is_data_change: false,
      is_system_event: false,
      notes: "Regular login",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
    },
  ],
  total: 1,
  page: 1,
  size: 10,
  pages: 1,
}

const mockAuditTrails = {
  items: [
    {
      id: 1,
      name: "User_1_UPDATE_2023_01_01",
      activity_log_id: 1,
      user_id: 1,
      changed_at: new Date("2023-01-01T00:00:00Z"),
      table_name: "User",
      record_id: 1,
      operation: "UPDATE",
      field_name: "email",
      old_value: "<EMAIL>",
      new_value: "<EMAIL>",
      change_reason: "User requested email change",
      change_context: { request_source: "profile_page" },
      is_sensitive: false,
      is_system_change: false,
      notes: "Email update",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
    },
  ],
  total: 1,
  page: 1,
  size: 10,
  pages: 1,
}

const mockRecordHistory = {
  table_name: "User",
  record_id: 1,
  total_changes: 3,
  first_change: new Date("2023-01-01T00:00:00Z"),
  last_change: new Date("2023-01-03T00:00:00Z"),
  changes: [
    {
      id: 1,
      name: "User_1_UPDATE_2023_01_01",
      activity_log_id: 1,
      user_id: 1,
      changed_at: new Date("2023-01-01T00:00:00Z"),
      table_name: "User",
      record_id: 1,
      operation: "UPDATE",
      field_name: "email",
      old_value: "<EMAIL>",
      new_value: "<EMAIL>",
      change_reason: "User requested email change",
      change_context: { request_source: "profile_page" },
      is_sensitive: false,
      is_system_change: false,
      notes: "Email update",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
    },
  ],
  change_summary: {
    UPDATE: 2,
    INSERT: 1,
  },
}

const mockAuditSummary = {
  total_activities: 100,
  total_data_changes: 50,
  security_events: 10,
  system_events: 5,
  user_actions: 85,
  failed_operations: 5,
  top_action_types: [
    { action_type: "LOGIN", count: 20 },
    { action_type: "UPDATE", count: 15 },
  ],
  top_users: [
    { user_id: 1, user_name: "john.doe", count: 25 },
    { user_id: 2, user_name: "jane.smith", count: 20 },
  ],
  activity_timeline: [
    { date: "2023-01-01", count: 10 },
    { date: "2023-01-02", count: 15 },
  ],
}

const mockUserActivitySummary = {
  user_id: 1,
  total_activities: 25,
  recent_activities: [mockActivityLogs.items[0]],
  top_actions: [
    { action_type: "LOGIN", count: 10 },
    { action_type: "UPDATE", count: 8 },
  ],
  security_events: 5,
  data_changes: 12,
  activity_timeline: [
    { date: "2023-01-01", count: 5 },
    { date: "2023-01-02", count: 8 },
  ],
}

// Create a wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  const TestWrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  TestWrapper.displayName = "TestWrapper"
  return TestWrapper
}

describe("useAudit hooks", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("useActivityLogs", () => {
    it("should fetch activity logs successfully", async () => {
      const mockGetActivityLogs = vi.mocked(auditApiClient.getActivityLogs)
      mockGetActivityLogs.mockResolvedValue(mockActivityLogs)

      const { result } = renderHook(() => useActivityLogs(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockActivityLogs)
      expect(mockGetActivityLogs).toHaveBeenCalledWith(undefined)
    })

    it("should fetch activity logs with filters", async () => {
      const mockGetActivityLogs = vi.mocked(auditApiClient.getActivityLogs)
      mockGetActivityLogs.mockResolvedValue(mockActivityLogs)

      const filters = {
        user_id: 1,
        action_types: ["LOGIN"],
        is_security_related: true,
      }

      const { result } = renderHook(() => useActivityLogs(filters), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetActivityLogs).toHaveBeenCalledWith(filters)
    })
  })

  describe("useCreateActivityLog", () => {
    it("should create activity log successfully", async () => {
      const mockCreateActivityLog = vi.mocked(auditApiClient.createActivityLog)
      const newActivityLog = {
        name: "TEST_LOG",
        action_type: "TEST",
        action_description: "Test activity log",
        user_id: 1,
        status: "SUCCESS",
        severity: "Info" as ErrorSeverityEnum,
        tags: ["test"],
        is_security_related: false,
        is_data_change: false,
        is_system_event: false,
      }

      mockCreateActivityLog.mockResolvedValue({
        ...mockActivityLogs.items[0],
        ...newActivityLog,
      })

      const { result } = renderHook(() => useCreateActivityLog(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(newActivityLog)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockCreateActivityLog).toHaveBeenCalledWith(newActivityLog)
    })
  })

  describe("useUpdateActivityLog", () => {
    it("should update activity log successfully", async () => {
      const mockUpdateActivityLog = vi.mocked(auditApiClient.updateActivityLog)
      const updateData = {
        status: "COMPLETED",
        execution_time_ms: 200,
      }

      mockUpdateActivityLog.mockResolvedValue({
        ...mockActivityLogs.items[0],
        ...updateData,
      })

      const { result } = renderHook(() => useUpdateActivityLog(), {
        wrapper: createWrapper(),
      })

      result.current.mutate({ id: 1, data: updateData })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockUpdateActivityLog).toHaveBeenCalledWith(1, updateData)
    })
  })

  describe("useAuditTrails", () => {
    it("should fetch audit trails successfully", async () => {
      const mockGetAuditTrails = vi.mocked(auditApiClient.getAuditTrails)
      mockGetAuditTrails.mockResolvedValue(mockAuditTrails)

      const { result } = renderHook(() => useAuditTrails(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockAuditTrails)
      expect(mockGetAuditTrails).toHaveBeenCalledWith(undefined)
    })

    it("should fetch audit trails with filters", async () => {
      const mockGetAuditTrails = vi.mocked(auditApiClient.getAuditTrails)
      mockGetAuditTrails.mockResolvedValue(mockAuditTrails)

      const filters = {
        table_name: "User",
        record_id: 1,
        operations: ["UPDATE"],
      }

      const { result } = renderHook(() => useAuditTrails(filters), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetAuditTrails).toHaveBeenCalledWith(filters)
    })
  })

  describe("useCreateAuditTrail", () => {
    it("should create audit trail successfully", async () => {
      const mockCreateAuditTrail = vi.mocked(auditApiClient.createAuditTrail)
      const newAuditTrail = {
        name: "TEST_AUDIT",
        table_name: "User",
        record_id: 1,
        operation: "Update",
        field_name: "name",
        old_value: "old_name",
        new_value: "new_name",
        is_sensitive: false,
        is_system_change: false,
      }

      mockCreateAuditTrail.mockResolvedValue({
        ...mockAuditTrails.items[0],
        ...newAuditTrail,
      })

      const { result } = renderHook(() => useCreateAuditTrail(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(newAuditTrail)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockCreateAuditTrail).toHaveBeenCalledWith(newAuditTrail)
    })
  })

  describe("useRecordHistory", () => {
    it("should fetch record history successfully", async () => {
      const mockGetRecordHistory = vi.mocked(auditApiClient.getRecordHistory)
      mockGetRecordHistory.mockResolvedValue(mockRecordHistory)

      const { result } = renderHook(() => useRecordHistory("User", 1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRecordHistory)
      expect(mockGetRecordHistory).toHaveBeenCalledWith("User", 1, undefined)
    })

    it("should not fetch when tableName or recordId is missing", () => {
      const mockGetRecordHistory = vi.mocked(auditApiClient.getRecordHistory)

      const { result } = renderHook(() => useRecordHistory("", 0), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockGetRecordHistory).not.toHaveBeenCalled()
    })
  })

  describe("useLogUserActivity", () => {
    it("should log user activity successfully", async () => {
      const mockLogUserActivity = vi.mocked(auditApiClient.logUserActivity)
      const activityData = {
        action_type: "LOGIN",
        action_description: "User logged in",
        category: "AUTHENTICATION",
      }

      mockLogUserActivity.mockResolvedValue(mockActivityLogs.items[0])

      const { result } = renderHook(() => useLogUserActivity(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(activityData)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLogUserActivity).toHaveBeenCalledWith(activityData)
    })
  })

  describe("useLogSecurityEvent", () => {
    it("should log security event successfully", async () => {
      const mockLogSecurityEvent = vi.mocked(auditApiClient.logSecurityEvent)
      const securityData = {
        action_type: "FAILED_LOGIN",
        action_description: "Failed login attempt",
        severity: "MEDIUM" as const,
        metadata: { ip: "***********" },
      }

      mockLogSecurityEvent.mockResolvedValue(mockActivityLogs.items[0])

      const { result } = renderHook(() => useLogSecurityEvent(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(securityData)

      await waitFor(
        () => {
          // Check that the mutation completed (either success or error)
          expect(result.current.isIdle).toBe(false)
        },
        { timeout: 3000 }
      )

      expect(mockLogSecurityEvent).toHaveBeenCalledWith(securityData)
    })
  })

  describe("useLogDataChange", () => {
    it("should log data change successfully", async () => {
      const mockLogDataChange = vi.mocked(auditApiClient.logDataChange)
      const changeData = {
        table_name: "User",
        record_id: 1,
        operation: "UPDATE" as const,
        field_name: "email",
        old_value: "<EMAIL>",
        new_value: "<EMAIL>",
        change_reason: "User requested change",
      }

      mockLogDataChange.mockResolvedValue(mockAuditTrails.items[0])

      const { result } = renderHook(() => useLogDataChange(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(changeData)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockLogDataChange).toHaveBeenCalledWith(changeData)
    })
  })

  describe("useAuditSummary", () => {
    it("should fetch audit summary successfully", async () => {
      const mockGetAuditSummary = vi.mocked(auditApiClient.getAuditSummary)
      mockGetAuditSummary.mockResolvedValue(mockAuditSummary)

      const { result } = renderHook(() => useAuditSummary(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockAuditSummary)
      expect(mockGetAuditSummary).toHaveBeenCalledWith(undefined)
    })

    it("should fetch audit summary with date range", async () => {
      const mockGetAuditSummary = vi.mocked(auditApiClient.getAuditSummary)
      mockGetAuditSummary.mockResolvedValue(mockAuditSummary)

      const params = {
        start_date: "2023-01-01",
        end_date: "2023-01-31",
      }

      const { result } = renderHook(() => useAuditSummary(params), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(mockGetAuditSummary).toHaveBeenCalledWith(params)
    })
  })

  describe("useUserActivitySummary", () => {
    it("should fetch user activity summary successfully", async () => {
      const mockGetUserActivitySummary = vi.mocked(
        auditApiClient.getUserActivitySummary
      )
      mockGetUserActivitySummary.mockResolvedValue(mockUserActivitySummary)

      const { result } = renderHook(() => useUserActivitySummary(1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockUserActivitySummary)
      expect(mockGetUserActivitySummary).toHaveBeenCalledWith(1, undefined)
    })

    it("should not fetch when userId is 0", () => {
      const mockGetUserActivitySummary = vi.mocked(
        auditApiClient.getUserActivitySummary
      )

      const { result } = renderHook(() => useUserActivitySummary(0), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockGetUserActivitySummary).not.toHaveBeenCalled()
    })
  })
})
