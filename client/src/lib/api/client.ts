/**
 * API Client for Ultimate Electrical Designer
 *
 * Centralized Axios-based HTTP client with request/response interceptors
 * for authentication, error handling, and consistent API communication.
 */

import type {
  LoginRequest,
  LoginResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  PasswordResetConfirm,
  PasswordResetRequest,
  PasswordResetResponse,
  RegisterRequest,
  RegisterResponse,
  UserRead,
} from "@/types/auth"

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
const API_VERSION = "v1"
const API_TIMEOUT = 10000 // 10 seconds

// Error response interface
export interface APIError {
  message: string
  code: string
  details?: Record<string, unknown>
}

class APIClient {
  private client: AxiosInstance
  private authToken: string | null = null

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: API_TIMEOUT,
      headers: {
        "Content-Type": "application/json",
      },
    })

    this.setupInterceptors()
  }

  /**
   * Set up request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor - add auth token if available
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token to requests if available
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`
        }

        // Add request ID for tracing
        config.headers["X-Request-ID"] = crypto.randomUUID()

        return config
      },
      (error) => {
        return Promise.reject(this.handleError(error))
      }
    )

    // Response interceptor - handle errors and format responses
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error) => {
        // Handle 401 Unauthorized - clear auth state
        if (error.response?.status === 401) {
          this.clearAuthToken()
          // Emit event for auth store to handle logout
          window.dispatchEvent(new CustomEvent("auth:unauthorized"))
        }

        // Handle network errors
        if (!error.response) {
          const networkError: APIError = {
            message: "Network error - please check your connection",
            code: "NETWORK_ERROR",
            details: { originalError: error.message },
          }
          return Promise.reject(networkError)
        }

        return Promise.reject(this.handleError(error))
      }
    )
  }

  /**
   * Standardize error handling across the application
   */
  private handleError(error: any): APIError {
    // If it's already our standardized error, return it
    if (error.code && error.message) {
      return error as APIError
    }

    // Handle Axios errors
    if (error.response) {
      const { data, status } = error.response

      // Backend validation errors
      if (status === 422 && data.detail) {
        return {
          message: Array.isArray(data.detail)
            ? data.detail.map((d: any) => d.msg).join(", ")
            : data.detail,
          code: "VALIDATION_ERROR",
          details: data.detail,
        }
      }

      // Other HTTP errors
      return {
        message: data.message || data.detail || `HTTP ${status} Error`,
        code: `HTTP_${status}`,
        details: data,
      }
    }

    // Generic error
    return {
      message: error.message || "An unexpected error occurred",
      code: "UNKNOWN_ERROR",
      details: { originalError: error },
    }
  }

  /**
   * Set authentication token for requests
   */
  setAuthToken(token: string): void {
    this.authToken = token
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.client.post("/auth/login", credentials)
    return response.data
  }

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    const response = await this.client.post("/auth/register", userData)
    return response.data
  }

  async logout(): Promise<LogoutResponse> {
    const response = await this.client.post("/auth/logout")
    return response.data
  }

  async getCurrentUser(): Promise<UserRead> {
    const response = await this.client.get("/auth/me")
    return response.data
  }

  async updateCurrentUser(userData: Partial<UserRead>): Promise<UserRead> {
    const response = await this.client.put("/auth/me", userData)
    return response.data
  }

  async changePassword(
    data: PasswordChangeRequest
  ): Promise<PasswordChangeResponse> {
    const response = await this.client.post("/auth/change-password", data)
    return response.data
  }

  async requestPasswordReset(
    data: PasswordResetRequest
  ): Promise<PasswordResetResponse> {
    const response = await this.client.post("/auth/password-reset", data)
    return response.data
  }

  async confirmPasswordReset(
    data: PasswordResetConfirm
  ): Promise<PasswordResetResponse> {
    const response = await this.client.post(
      "/auth/password-reset/confirm",
      data
    )
    return response.data
  }

  // Generic request methods for other API calls
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config)
    return response.data
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.post(url, data, config)
    return response.data
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.put(url, data, config)
    return response.data
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config)
    return response.data
  }

  async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.client.patch(url, data, config)
    return response.data
  }
}

// Create singleton instance
export const apiClient = new APIClient()

// Export for testing and advanced usage
export default apiClient
