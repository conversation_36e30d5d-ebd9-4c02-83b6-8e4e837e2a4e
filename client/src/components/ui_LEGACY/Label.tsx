/**
 * Label Atom - Universal Foundational Component
 * 
 * Atomic design label component providing core label functionality
 * with proper input association, typography, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Required field indication support
 * - Consistent typography system
 * - Proper input association
 * - Performance optimized with minimal footprint
 * - Full backward compatibility support
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Label as LabelPrimitive } from "@radix-ui/react-label"

import { cn } from "@/lib/utils"

// Label variants using CVA for consistent styling
const labelVariants = cva(
  "text-foreground leading-4 font-medium select-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50",
  {
    variants: {
      size: {
        sm: "text-xs leading-3",
        default: "text-sm leading-4",
        lg: "text-base leading-5",
      },
      weight: {
        normal: "font-normal",
        medium: "font-medium",
        semibold: "font-semibold",
      },
      state: {
        default: "",
        error: "text-destructive",
        warning: "text-yellow-700",
        success: "text-green-700",
      },
    },
    defaultVariants: {
      size: "default",
      weight: "medium", 
      state: "default",
    },
  }
)

export interface LabelProps
  extends React.ComponentPropsWithoutRef<typeof LabelPrimitive>,
    VariantProps<typeof labelVariants> {
  /** Whether the field is required */
  required?: boolean
  /** Validation state of the associated field */
  state?: "default" | "error" | "warning" | "success"
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive>,
  LabelProps
>(
  (
    {
      className,
      children,
      size,
      weight,
      state,
      required = false,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    return (
      <LabelPrimitive
        ref={ref}
        className={cn(labelVariants({ size, weight, state }), className)}
        data-testid={testId}
        {...props}
      >
        {children}
        {required && (
          <span
            className="text-destructive ml-1"
            aria-label="required"
            title="This field is required"
          >
            *
          </span>
        )}
      </LabelPrimitive>
    )
  }
)

Label.displayName = "Label"

// Export types for external use
export type LabelSize = NonNullable<LabelProps["size"]>
export type LabelWeight = NonNullable<LabelProps["weight"]>
export type LabelState = NonNullable<LabelProps["state"]>

// Export variants for external use
export { labelVariants }