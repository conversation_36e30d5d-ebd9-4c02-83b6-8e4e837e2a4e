/**
 * Avatar Atom - User Representation Component
 * 
 * Atomic design avatar component providing user representation
 * with engineering-grade quality and electrical design context.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with image loading states
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Role-based visual indicators for electrical teams
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { <PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, <PERSON>ap, Shield, Eye, Wrench } from "lucide-react"

import { cn } from "@/lib/utils"

// Avatar variants using CVA for consistent styling
const avatarVariants = cva(
  "relative inline-flex items-center justify-center overflow-hidden rounded-full bg-muted text-muted-foreground font-medium transition-all duration-200 shrink-0",
  {
    variants: {
      size: {
        xs: "h-6 w-6 text-xs",
        sm: "h-8 w-8 text-sm",
        md: "h-10 w-10 text-sm",
        lg: "h-12 w-12 text-base",
        xl: "h-16 w-16 text-lg",
        "2xl": "h-20 w-20 text-xl",
      },
      variant: {
        default: "bg-muted text-muted-foreground",
        primary: "bg-primary/10 text-primary border border-primary/20",
        secondary: "bg-secondary text-secondary-foreground",
        // Electrical role variants
        engineer: "bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-950/50 dark:text-blue-300 dark:border-blue-800",
        technician: "bg-orange-50 text-orange-700 border border-orange-200 dark:bg-orange-950/50 dark:text-orange-300 dark:border-orange-800",
        inspector: "bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-950/50 dark:text-purple-300 dark:border-purple-800",
        manager: "bg-emerald-50 text-emerald-700 border border-emerald-200 dark:bg-emerald-950/50 dark:text-emerald-300 dark:border-emerald-800",
        admin: "bg-red-50 text-red-700 border border-red-200 dark:bg-red-950/50 dark:text-red-300 dark:border-red-800",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
)

// Role indicator position variants
const roleIndicatorVariants = cva(
  "absolute flex items-center justify-center rounded-full border-2 border-background",
  {
    variants: {
      size: {
        xs: "h-3 w-3 -bottom-0.5 -right-0.5",
        sm: "h-3.5 w-3.5 -bottom-0.5 -right-0.5", 
        md: "h-4 w-4 -bottom-1 -right-1",
        lg: "h-5 w-5 -bottom-1 -right-1",
        xl: "h-6 w-6 -bottom-1.5 -right-1.5",
        "2xl": "h-7 w-7 -bottom-2 -right-2",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

// Role to icon mapping for electrical engineering teams
const roleIconMap = {
  engineer: Zap,
  technician: Wrench,
  inspector: Eye,
  manager: UserCheck,
  admin: Shield,
  default: User,
} as const

// Status indicator colors
const statusColors = {
  online: "bg-green-500",
  away: "bg-yellow-500",
  busy: "bg-red-500",
  offline: "bg-gray-400",
} as const

// Generate initials from name
const generateInitials = (name: string): string => {
  if (!name) return "U"
  
  const parts = name.trim().split(/\s+/)
  if (parts.length === 1) {
    return parts[0].charAt(0).toUpperCase()
  }
  
  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase()
}

// Get role-specific background color
const getRoleColor = (role?: string): string => {
  const roleColors: Record<string, string> = {
    engineer: "bg-blue-500",
    technician: "bg-orange-500", 
    inspector: "bg-purple-500",
    manager: "bg-emerald-500",
    admin: "bg-red-500",
  }
  return roleColors[role || ""] || "bg-gray-500"
}

export interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof avatarVariants> {
  /** User's name for generating initials and accessibility */
  name?: string
  /** Avatar image source */
  src?: string
  /** Alt text for avatar image */
  alt?: string
  /** User's role for visual indicator */
  role?: "engineer" | "technician" | "inspector" | "manager" | "admin"
  /** Online status indicator */
  status?: "online" | "away" | "busy" | "offline"
  /** Show role indicator badge */
  showRoleIndicator?: boolean
  /** Show status indicator */
  showStatus?: boolean
  /** Custom fallback content */
  fallback?: React.ReactNode
  /** Loading state */
  loading?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  (
    {
      variant = "default",
      size = "md",
      name,
      src,
      alt,
      role,
      status,
      showRoleIndicator = false,
      showStatus = false,
      fallback,
      loading = false,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const [imageError, setImageError] = React.useState(false)
    const [imageLoading, setImageLoading] = React.useState(!!src)
    
    const initials = React.useMemo(() => generateInitials(name || ""), [name])
    const RoleIcon = role ? roleIconMap[role] : roleIconMap.default
    
    const handleImageLoad = React.useCallback(() => {
      setImageLoading(false)
      setImageError(false)
    }, [])
    
    const handleImageError = React.useCallback(() => {
      setImageLoading(false)
      setImageError(true)
    }, [])
    
    const showImage = src && !imageError && !loading
    const showFallback = !showImage || imageLoading
    
    // Icon size based on avatar size
    const iconSize = {
      xs: "h-3 w-3",
      sm: "h-4 w-4",
      md: "h-5 w-5", 
      lg: "h-6 w-6",
      xl: "h-8 w-8",
      "2xl": "h-10 w-10",
    }[size || "md"]
    
    const roleIndicatorIconSize = {
      xs: "h-2 w-2",
      sm: "h-2 w-2",
      md: "h-2.5 w-2.5",
      lg: "h-3 w-3",
      xl: "h-3.5 w-3.5", 
      "2xl": "h-4 w-4",
    }[size || "md"]
    
    return (
      <div
        ref={ref}
        className={cn(
          avatarVariants({ 
            variant: role && variant === "default" ? role : variant, 
            size 
          }), 
          className
        )}
        data-testid={testId || "avatar"}
        role="img"
        aria-label={alt || `Avatar for ${name || "user"}`}
        {...props}
      >
        {/* Loading state */}
        {loading && (
          <div className="absolute inset-0 animate-pulse bg-muted rounded-full" />
        )}
        
        {/* Avatar image */}
        {showImage && (
          <img
            src={src}
            alt={alt || `${name}'s avatar`}
            className="h-full w-full object-cover rounded-full"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        )}
        
        {/* Fallback content */}
        {showFallback && !loading && (
          <>
            {fallback || (
              <>
                {name ? (
                  <span className="select-none" aria-hidden="true">
                    {initials}
                  </span>
                ) : (
                  <User className={iconSize} aria-hidden="true" />
                )}
              </>
            )}
          </>
        )}
        
        {/* Role indicator badge */}
        {showRoleIndicator && role && (
          <div
            className={cn(
              roleIndicatorVariants({ size }),
              getRoleColor(role)
            )}
            title={`Role: ${role.charAt(0).toUpperCase() + role.slice(1)}`}
          >
            <RoleIcon 
              className={cn(roleIndicatorIconSize, "text-white")} 
              aria-hidden="true" 
            />
          </div>
        )}
        
        {/* Status indicator */}
        {showStatus && status && (
          <div
            className={cn(
              roleIndicatorVariants({ size }),
              statusColors[status]
            )}
            title={`Status: ${status.charAt(0).toUpperCase() + status.slice(1)}`}
            aria-label={`User status: ${status}`}
          />
        )}
      </div>
    )
  }
)

Avatar.displayName = "Avatar"

// Avatar Group Component for showing multiple avatars
export interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Array of avatar props */
  avatars: (AvatarProps & { id: string })[]
  /** Maximum number of avatars to show */
  max?: number
  /** Size of avatars in group */
  size?: AvatarProps["size"]
  /** Overlap amount */
  spacing?: "tight" | "normal" | "loose"
  /** Show count of remaining avatars */
  showCount?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
  (
    {
      avatars,
      max = 5,
      size = "md",
      spacing = "normal",
      showCount = true,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const visibleAvatars = avatars.slice(0, max)
    const remainingCount = Math.max(0, avatars.length - max)
    
    const spacingClasses = {
      tight: "-space-x-1",
      normal: "-space-x-2", 
      loose: "-space-x-1",
    }[spacing]
    
    const ringClasses = {
      xs: "ring-2",
      sm: "ring-2",
      md: "ring-2",
      lg: "ring-2", 
      xl: "ring-2",
      "2xl": "ring-2",
    }[size || "md"]
    
    return (
      <div
        ref={ref}
        className={cn("flex items-center", spacingClasses, className)}
        data-testid={testId || "avatar-group"}
        {...props}
      >
        {visibleAvatars.map(({ id, ...avatarProps }) => (
          <Avatar
            key={id}
            size={size}
            className={cn("ring-background", ringClasses)}
            data-testid={`avatar-group-item-${id}`}
            {...avatarProps}
          />
        ))}
        
        {/* Remaining count indicator */}
        {showCount && remainingCount > 0 && (
          <div
            className={cn(
              avatarVariants({ size, variant: "secondary" }),
              "ring-background",
              ringClasses
            )}
            title={`${remainingCount} more user${remainingCount === 1 ? "" : "s"}`}
          >
            <span className="text-xs font-semibold">
              +{remainingCount}
            </span>
          </div>
        )}
      </div>
    )
  }
)

AvatarGroup.displayName = "AvatarGroup"

// Export types for external use
export type AvatarVariant = NonNullable<AvatarProps["variant"]>
export type AvatarSize = NonNullable<AvatarProps["size"]>
export type UserRole = NonNullable<AvatarProps["role"]>
export type UserStatus = NonNullable<AvatarProps["status"]>

// Export variants and configurations for external use
export { avatarVariants, roleIndicatorVariants, roleIconMap, statusColors }

// Utility functions for electrical engineering teams
export const getElectricalRoleIcon = (role: string) => {
  return roleIconMap[role as keyof typeof roleIconMap] || roleIconMap.default
}

export const getElectricalRoleColor = (role: string): string => {
  const roleColorMap: Record<string, string> = {
    engineer: "text-blue-600",
    technician: "text-orange-600",
    inspector: "text-purple-600", 
    manager: "text-emerald-600",
    admin: "text-red-600",
  }
  return roleColorMap[role] || "text-gray-600"
}

export const isValidElectricalRole = (role: string): role is UserRole => {
  const validRoles: UserRole[] = ["engineer", "technician", "inspector", "manager", "admin"]
  return validRoles.includes(role as UserRole)
}