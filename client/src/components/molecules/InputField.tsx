/**
 * InputField Molecule - Compound Component
 * 
 * Atomic design molecule combining Label + Input atoms with integrated
 * validation display and consistent styling.
 * 
 * Features:
 * - Atomic design composition (Label + Input)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Integrated validation states (error, warning, success)
 * - Help text support
 * - Consistent sizing system
 * - Performance optimized
 * - Engineering-grade quality
 */

import React from "react"
import { AlertCircle, CheckCircle, Info } from "lucide-react"

import { cn } from "@/lib/utils"
import { Label, type LabelProps } from "../ui/Label"
import { Input, type InputProps } from "../ui/Input"

export interface InputFieldProps extends Omit<InputProps, "id" | "state"> {
  /** Label text for the input field */
  label: string
  /** Whether the field is required */
  required?: boolean
  /** Error message to display */
  error?: string
  /** Warning message to display */
  warning?: string
  /** Success message to display */
  success?: string
  /** Help text to display when no validation messages are shown */
  helpText?: string
  /** Whether the field has been touched (affects validation display) */
  touched?: boolean
  /** Override the generated input ID */
  id?: string
  /** Label props (for advanced customization) */
  labelProps?: Partial<LabelProps>
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
  (
    {
      label,
      required = false,
      error,
      warning,
      success,
      helpText,
      touched = false,
      inputSize = "default",
      variant = "default",
      className,
      id,
      labelProps,
      "data-testid": testId,
      ...inputProps
    },
    ref
  ) => {
    // Generate unique ID if not provided
    const inputId = id || `input-field-${label.toLowerCase().replace(/\s+/g, "-")}`
    
    // Determine validation state
    const hasError = error && touched
    const hasWarning = warning && touched && !hasError
    const hasSuccess = success && touched && !hasError && !hasWarning
    
    // Determine input state
    let inputState: InputProps["state"] = "default"
    if (hasError) inputState = "error"
    else if (hasWarning) inputState = "warning" 
    else if (hasSuccess) inputState = "success"
    
    // Determine label state
    let labelState: LabelProps["state"] = "default"
    if (hasError) labelState = "error"
    else if (hasWarning) labelState = "warning"
    else if (hasSuccess) labelState = "success"

    return (
      <div className="space-y-2" data-testid={testId}>
        <Label
          htmlFor={inputId}
          required={required}
          size={inputSize === "lg" ? "lg" : inputSize === "sm" ? "sm" : "default"}
          state={labelState}
          data-testid={testId ? `${testId}-label` : undefined}
          {...labelProps}
        >
          {label}
        </Label>

        <div className="relative">
          <Input
            ref={ref}
            id={inputId}
            inputSize={inputSize}
            state={inputState}
            variant={variant}
            className={cn("pr-10", className)}
            data-testid={testId ? `${testId}-input` : undefined}
            {...inputProps}
          />

          {/* Status Icon */}
          {(hasError || hasWarning || hasSuccess) && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {hasError && (
                <AlertCircle 
                  className="size-4 text-destructive" 
                  aria-hidden="true"
                />
              )}
              {hasWarning && (
                <Info 
                  className="size-4 text-yellow-600" 
                  aria-hidden="true"
                />
              )}
              {hasSuccess && (
                <CheckCircle 
                  className="size-4 text-green-600" 
                  aria-hidden="true"
                />
              )}
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="space-y-1">
          {hasError && (
            <p
              className={cn(
                "text-sm text-destructive flex items-center gap-1.5",
                inputSize === "sm" && "text-xs",
                inputSize === "lg" && "text-base"
              )}
              role="alert"
              aria-live="polite"
              data-testid={testId ? `${testId}-error` : undefined}
            >
              <AlertCircle className="size-3 flex-shrink-0" aria-hidden="true" />
              {error}
            </p>
          )}

          {hasWarning && (
            <p
              className={cn(
                "text-sm text-yellow-700 flex items-center gap-1.5",
                inputSize === "sm" && "text-xs",
                inputSize === "lg" && "text-base"
              )}
              role="alert"
              aria-live="polite"
              data-testid={testId ? `${testId}-warning` : undefined}
            >
              <Info className="size-3 flex-shrink-0" aria-hidden="true" />
              {warning}
            </p>
          )}

          {hasSuccess && (
            <p
              className={cn(
                "text-sm text-green-700 flex items-center gap-1.5",
                inputSize === "sm" && "text-xs",
                inputSize === "lg" && "text-base"
              )}
              role="status"
              aria-live="polite" 
              data-testid={testId ? `${testId}-success` : undefined}
            >
              <CheckCircle className="size-3 flex-shrink-0" aria-hidden="true" />
              {success}
            </p>
          )}

          {helpText && !hasError && !hasWarning && !hasSuccess && (
            <p
              className={cn(
                "text-sm text-muted-foreground",
                inputSize === "sm" && "text-xs",
                inputSize === "lg" && "text-base"
              )}
              data-testid={testId ? `${testId}-help` : undefined}
            >
              {helpText}
            </p>
          )}
        </div>
      </div>
    )
  }
)

InputField.displayName = "InputField"