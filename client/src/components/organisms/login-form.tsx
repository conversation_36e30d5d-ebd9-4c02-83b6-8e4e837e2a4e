/**
 * @file LoginForm organism component
 * @description Complete login form with validation, auth integration, and professional UI
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/atoms/button'
import { UnifiedIcon as Icon } from '@/components/atoms/icon'
import { InputField } from '@/components/molecules/input-field'
import { PasswordInput } from '@/components/molecules/password-input'
import { useLogin } from '@/hooks/api/useAuth'
import { useAuthStore } from '@/stores/authStore'
import { validateLoginForm } from '@/lib/validation/auth'
import type { ValidationErrors, LoginRequest, LoginFormData } from '@/types/auth'

export interface LoginFormProps {
  /** Custom title for the form */
  title?: string
  /** Custom description for the form */
  description?: string
  /** Show forgot password link */
  showForgotPassword?: boolean
  /** Show sign up link */
  showSignUpLink?: boolean
  /** Callback fired on successful login */
  onSuccess?: () => void
  /** Callback fired on login error */
  onError?: (error: any) => void
  /** Callback fired when forgot password link is clicked */
  onForgotPassword?: () => void
  /** Callback fired when sign up link is clicked */
  onSignUp?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  'data-testid'?: string
}

export const LoginForm = React.forwardRef<HTMLFormElement, LoginFormProps>(
  (
    {
      title = 'Welcome Back',
      description = 'Sign in to your account to continue',
      showForgotPassword = true,
      showSignUpLink = true,
      onSuccess,
      onError,
      onForgotPassword,
      onSignUp,
      className,
      'data-testid': dataTestId,
    },
    ref
  ) => {
    // Auth hooks and state
    const login = useLogin()
    const { isLoading } = useAuthStore()

    // Form state
    const [formData, setFormData] = React.useState<LoginFormData>({
      username: '',
      password: '',
    })

    // Validation state
    const [errors, setErrors] = React.useState<ValidationErrors>({})
    const [isValidating, setIsValidating] = React.useState(false)

    // Handle input changes
    const handleInputChange = React.useCallback(
      (field: keyof LoginFormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value
        
        setFormData(prev => ({
          ...prev,
          [field]: value,
        }))

        // Clear field error when user starts typing
        if (errors[field]) {
          setErrors(prev => {
            const newErrors = { ...prev }
            delete newErrors[field]
            return newErrors
          })
        }
      },
      [errors]
    )

    // Validate form
    const validateForm = React.useCallback((): boolean => {
      setIsValidating(true)
      
      try {
        const validationErrors = validateLoginForm(formData)
        setErrors(validationErrors)
        return Object.keys(validationErrors).length === 0
      } catch (error) {
        // Handle unexpected errors
        setErrors({ form: 'Validation failed' })
        return false
      } finally {
        setIsValidating(false)
      }
    }, [formData])

    // Handle form submission
    const handleSubmit = React.useCallback(
      async (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault()
        
        // Validate form
        if (!validateForm()) {
          return
        }

        // Submit login - convert to LoginRequest for API
        login.mutate({
          email: formData.username,
          password: formData.password,
        })
      },
      [formData, login, validateForm]
    )

    // Handle login success
    React.useEffect(() => {
      if (login.isSuccess) {
        onSuccess?.()
      }
    }, [login.isSuccess, onSuccess])

    // Handle login error
    React.useEffect(() => {
      if (login.error) {
        onError?.(login.error)
      }
    }, [login.error, onError])

    // Determine loading state
    const isSubmitting = login.isPending || isLoading
    const hasServerError = Boolean(login.error)

    return (
      <form
        ref={ref}
        onSubmit={handleSubmit}
        className={cn(
          'w-full max-w-md space-y-6 rounded-lg border bg-card p-6 shadow-sm',
          className
        )}
        data-testid={dataTestId}
        noValidate
      >
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight text-foreground">
            {title}
          </h1>
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>

        {/* Server Error Display */}
        {hasServerError && (
          <div
            role="alert"
            className="rounded-md border border-destructive bg-destructive/10 p-3"
          >
            <div className="flex items-center space-x-2">
              <Icon type="alert" className="h-4 w-4 text-destructive" />
              <p className="text-sm text-destructive">
                {login.error?.message || 'An error occurred during login'}
              </p>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Email Field */}
          <InputField
            label="Email Address"
            type="email"
            value={formData.username}
            onChange={handleInputChange('username')}
            error={errors.username}
            required
            disabled={isSubmitting}
            placeholder="Enter your email address"
            autoComplete="email"
            className="w-full"
          />

          {/* Password Field */}
          <PasswordInput
            label="Password"
            value={formData.password}
            onChange={handleInputChange('password')}
            error={errors.password}
            required
            disabled={isSubmitting}
            placeholder="Enter your password"
            autoComplete="current-password"
            className="w-full"
          />
        </div>

        {/* Forgot Password Link */}
        {showForgotPassword && (
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onForgotPassword}
              className="text-sm text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              disabled={isSubmitting}
            >
              Forgot password?
            </button>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          disabled={isSubmitting || isValidating}
          aria-disabled={isSubmitting || isValidating}
        >
          {isSubmitting ? (
            <>
              <Icon type="refresh" className="mr-2 h-4 w-4 animate-spin" />
              Signing in...
            </>
          ) : (
            <>
              <Icon type="login" className="mr-2 h-4 w-4" />
              Sign in
            </>
          )}
        </Button>

        {/* Sign Up Link */}
        {showSignUpLink && (
          <div className="text-center text-sm text-muted-foreground">
            Don't have an account?{' '}
            <button
              type="button"
              onClick={onSignUp}
              className="font-medium text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              disabled={isSubmitting}
            >
              Sign up
            </button>
          </div>
        )}
      </form>
    )
  }
)

LoginForm.displayName = 'LoginForm'