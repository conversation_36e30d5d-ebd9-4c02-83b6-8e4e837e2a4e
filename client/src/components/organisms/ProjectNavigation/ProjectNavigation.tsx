/**
 * Project Navigation Organism
 *
 * Comprehensive electrical project navigation interface combining atoms and molecules
 * to create a professional-grade project workflow navigation system.
 *
 * Features:
 * - Professional electrical engineering project navigation
 * - IEEE/IEC standards compliance tracking
 * - Phase-based project workflow
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with virtualization
 * - Team collaboration integration
 * - Real-time progress tracking
 * - Multi-phase project management
 */

import React, { Suspense } from "react"

import type {
  NavigationItem,
  ProjectInfo,
  ProjectNavigationProps,
  ProjectPhase,
  TeamMember,
} from "./ProjectNavigationTypes"

import {
  Activity,
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Bell,
  Briefcase,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Clock,
  FileText,
  FolderOpen,
  Home,
  MapPin,
  MoreHorizontal,
  Settings,
  Shield,
  Target,
  TrendingUp,
  User,
  Users,
  Zap,
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"

import { cn } from "@/lib/utils"

import { Button } from "@/components/atoms/Button"
import { ProgressBar } from "@/components/atoms/ProgressBar"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { HealthIndicator } from "@/components/molecules/HealthIndicator"
import { SearchBox } from "@/components/molecules/SearchBox"
import { Avatar } from "@/components/ui_LEGACY/Avatar"
import { Badge } from "@/components/ui_LEGACY/Badge"

import { useProjectNavigation } from "./useProjectNavigation"

// Project Header Component
const ProjectHeader: React.FC<{
  project: ProjectInfo
  progress: number
  showProgress: boolean
  showTeam: boolean
  teamMembers: ReadonlyArray<TeamMember>
  onSettingsClick: () => void
  onTeamClick: () => void
}> = ({
  project,
  progress,
  showProgress,
  showTeam,
  teamMembers,
  onSettingsClick,
  onTeamClick,
}) => (
  <div className="bg-card border-b p-4">
    <div className="mb-3 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <FolderOpen className="text-primary h-5 w-5" />
          <div>
            <h2 className="text-lg leading-none font-semibold">
              {project.name}
            </h2>
            <p className="text-muted-foreground mt-1 text-sm">
              {project.code} • {project.client}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" size="sm">
            {project.status.replace("_", " ")}
          </Badge>
          <Badge
            variant={
              project.priority === "critical"
                ? "destructive"
                : project.priority === "high"
                  ? "secondary"
                  : "outline"
            }
            size="sm"
          >
            {project.priority}
          </Badge>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {showTeam && teamMembers.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onTeamClick}
            className="h-8"
          >
            <Users className="mr-1 h-4 w-4" />
            {teamMembers.length}
          </Button>
        )}

        <Button variant="ghost" size="sm" onClick={onSettingsClick}>
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>

    {showProgress && (
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Overall Progress</span>
          <span className="font-medium">{Math.round(progress)}%</span>
        </div>
        <ProgressBar value={progress} className="h-2" />
      </div>
    )}

    <div className="text-muted-foreground mt-3 flex items-center gap-4 text-sm">
      <div className="flex items-center gap-1">
        <MapPin className="h-3 w-3" />
        {project.location}
      </div>
      <div className="flex items-center gap-1">
        <Calendar className="h-3 w-3" />
        {project.estimatedEndDate
          ? `Due ${project.estimatedEndDate.toLocaleDateString()}`
          : "No end date"}
      </div>
      {project.applicableStandards.length > 0 && (
        <div className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          {project.applicableStandards.slice(0, 2).join(", ")}
          {project.applicableStandards.length > 2 &&
            ` +${project.applicableStandards.length - 2}`}
        </div>
      )}
    </div>
  </div>
)

// Navigation Item Component
const NavigationItemComponent: React.FC<{
  item: NavigationItem
  level: number
  isExpanded: boolean
  onToggleExpanded: () => void
  onItemClick: (item: NavigationItem) => void
}> = ({ item, level, isExpanded, onToggleExpanded, onItemClick }) => {
  const hasChildren = item.children && item.children.length > 0
  const IconComponent = item.icon || FileText

  return (
    <div>
      <div
        className={cn(
          "flex cursor-pointer items-center gap-2 rounded-md p-2 text-sm transition-colors",
          "hover:bg-muted/50",
          item.isActive &&
            "bg-primary/10 text-primary border-l-primary border-l-2",
          item.isDisabled && "cursor-not-allowed opacity-50",
          level > 0 && `ml-${level * 4}`
        )}
        onClick={() => !item.isDisabled && onItemClick(item)}
      >
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpanded()
            }}
            className="hover:bg-muted rounded p-0.5"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </button>
        )}

        <div className="flex min-w-0 flex-1 items-center gap-2">
          <IconComponent
            className={cn(
              "h-4 w-4 flex-shrink-0",
              item.isCompleted
                ? "text-green-600"
                : item.hasIssues
                  ? "text-red-500"
                  : "text-muted-foreground"
            )}
          />

          <span className="flex-1 truncate">{item.label}</span>

          {item.isCompleted && (
            <CheckCircle className="h-3 w-3 flex-shrink-0 text-green-600" />
          )}

          {item.hasIssues && (
            <Badge variant="destructive" size="sm" className="flex-shrink-0">
              {item.issueCount}
            </Badge>
          )}

          {typeof item.progress === "number" && !item.isCompleted && (
            <div className="flex flex-shrink-0 items-center gap-1">
              <div className="bg-muted h-1 w-8 overflow-hidden rounded-full">
                <div
                  className="bg-primary h-full transition-all"
                  style={{ width: `${item.progress}%` }}
                />
              </div>
              <span className="text-muted-foreground text-xs">
                {Math.round(item.progress)}%
              </span>
            </div>
          )}
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="ml-4 space-y-1">
          {item.children!.map((child) => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              level={level + 1}
              isExpanded={false}
              onToggleExpanded={() => {}}
              onItemClick={onItemClick}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Navigation Breadcrumbs Component
const NavigationBreadcrumbs: React.FC<{
  breadcrumbs: ReadonlyArray<NavigationItem>
  onItemClick: (item: NavigationItem) => void
}> = ({ breadcrumbs, onItemClick }) => {
  if (breadcrumbs.length === 0) return null

  return (
    <div className="text-muted-foreground flex items-center gap-1 border-b p-3 text-sm">
      <Home className="h-3 w-3" />
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={item.id}>
          {index > 0 && <ChevronRight className="h-3 w-3" />}
          <button
            onClick={() => onItemClick(item)}
            className="hover:text-foreground truncate transition-colors"
          >
            {item.label}
          </button>
        </React.Fragment>
      ))}
    </div>
  )
}

// Team Panel Component
const TeamPanel: React.FC<{
  team: ReadonlyArray<TeamMember>
  showWorkload: boolean
  onMemberClick: (member: TeamMember) => void
}> = ({ team, showWorkload, onMemberClick }) => (
  <div className="bg-card border-t p-4">
    <div className="mb-3 flex items-center justify-between">
      <h3 className="text-sm font-medium">Project Team</h3>
      <Badge variant="outline" size="sm">
        {team.filter((m) => m.isActive).length}/{team.length}
      </Badge>
    </div>

    <div className="space-y-2">
      {team.slice(0, 6).map((member) => (
        <div
          key={member.id}
          className="hover:bg-muted/50 flex cursor-pointer items-center gap-2 rounded-md p-2 transition-colors"
          onClick={() => onMemberClick(member)}
        >
          <Avatar
            src={member.avatar}
            alt={member.name}
            size="sm"
            className="flex-shrink-0"
          />

          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <p className="truncate text-sm font-medium">{member.name}</p>
              <StatusIndicator
                variant={member.isOnline ? "operational" : "offline"}
                size="sm"
              />
            </div>
            <p className="text-muted-foreground text-xs">
              {member.role.replace("_", " ")}
            </p>
          </div>

          {showWorkload && typeof member.workload === "number" && (
            <div className="flex flex-shrink-0 items-center gap-1">
              <div className="bg-muted h-1 w-8 overflow-hidden rounded-full">
                <div
                  className={cn(
                    "h-full transition-all",
                    member.workload > 80
                      ? "bg-red-500"
                      : member.workload > 60
                        ? "bg-amber-500"
                        : "bg-green-500"
                  )}
                  style={{ width: `${member.workload}%` }}
                />
              </div>
              <span className="text-muted-foreground text-xs">
                {member.workload}%
              </span>
            </div>
          )}
        </div>
      ))}

      {team.length > 6 && (
        <Button variant="ghost" size="sm" className="mt-2 w-full">
          View All ({team.length})
        </Button>
      )}
    </div>
  </div>
)

// Error Fallback Component
const ErrorFallback: React.FC<{
  error: Error
  resetErrorBoundary: () => void
}> = ({ error, resetErrorBoundary }) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <AlertTriangle className="mb-4 h-12 w-12 text-red-500" />
    <h3 className="mb-2 text-lg font-semibold">Navigation Error</h3>
    <p className="text-muted-foreground mb-4">
      {error.message || "An unexpected error occurred"}
    </p>
    <Button onClick={resetErrorBoundary}>Try Again</Button>
  </div>
)

// Main Project Navigation Component
export const ProjectNavigation = React.forwardRef<
  HTMLDivElement,
  ProjectNavigationProps
>(
  (
    {
      project: externalProject,
      config: externalConfig,
      activeItemId: externalActiveItemId,
      loading: externalLoading = false,
      error: externalError = null,
      onItemClick,
      onPhaseChange,
      onTeamMemberClick,
      onProjectSettings,
      onNotificationClick,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Use internal hook if no external data provided
    const hookData = useProjectNavigation({
      projectId: externalProject?.id || "",
      enableRealTime: true,
      enableNotifications: true,
    })

    // Use external props or fall back to hook data
    const project = externalProject || hookData.project
    const config = externalConfig || hookData.config
    const activeItemId = externalActiveItemId || hookData.activeItemId
    const loading = externalLoading || hookData.loading
    const error = externalError || hookData.error

    // Use external callbacks or hook callbacks
    const handleItemClick = onItemClick || hookData.navigateToItem
    const handlePhaseChange = onPhaseChange || hookData.navigateToPhase
    const handleTeamMemberClick =
      onTeamMemberClick || ((member: TeamMember) => {})
    const handleProjectSettings = onProjectSettings || (() => {})
    const handleNotificationClick = onNotificationClick || (() => {})

    const [expandedItems, setExpandedItems] = React.useState<Set<string>>(
      new Set()
    )

    const toggleItemExpanded = (itemId: string) => {
      setExpandedItems((prev) => {
        const next = new Set(prev)
        if (next.has(itemId)) {
          next.delete(itemId)
        } else {
          next.add(itemId)
        }
        return next
      })
    }

    if (!project) {
      return (
        <div
          ref={ref}
          className={cn("flex items-center justify-center p-8", className)}
          data-testid={testId}
          {...props}
        >
          {loading ? (
            <div className="space-y-4">
              <div className="bg-muted h-16 animate-pulse rounded-lg" />
              <div className="space-y-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div
                    key={i}
                    className="bg-muted/60 h-8 animate-pulse rounded"
                  />
                ))}
              </div>
            </div>
          ) : error ? (
            <ErrorFallback
              error={new Error(error)}
              resetErrorBoundary={() => hookData.refreshProject()}
            />
          ) : (
            <div className="text-muted-foreground text-center">
              <Briefcase className="mx-auto mb-3 h-12 w-12 opacity-50" />
              <p>No project data available</p>
            </div>
          )}
        </div>
      )
    }

    return (
      <ErrorBoundary
        FallbackComponent={ErrorFallback}
        onReset={hookData.refreshProject}
      >
        <nav
          ref={ref}
          className={cn(
            "bg-background flex h-full flex-col border-r",
            config.compactMode && "w-64",
            !config.compactMode && "w-80",
            className
          )}
          data-testid={testId || "project-navigation"}
          {...props}
        >
          {/* Project Header */}
          <ProjectHeader
            project={project}
            progress={hookData.overallProgress}
            showProgress={config.showProgress}
            showTeam={config.showTeamMembers}
            teamMembers={hookData.activeTeamMembers}
            onSettingsClick={handleProjectSettings}
            onTeamClick={hookData.toggleTeamPanel}
          />

          {/* Breadcrumbs */}
          <NavigationBreadcrumbs
            breadcrumbs={hookData.breadcrumbs}
            onItemClick={handleItemClick}
          />

          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto">
            <Suspense
              fallback={<div className="p-4">Loading navigation...</div>}
            >
              {loading && hookData.navigationItems.length === 0 ? (
                <div className="space-y-2 p-4">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-muted/50 h-8 animate-pulse rounded"
                    />
                  ))}
                </div>
              ) : hookData.navigationItems.length === 0 ? (
                <div className="px-4 py-12 text-center">
                  <Target className="text-muted-foreground mx-auto mb-4 h-12 w-12 opacity-50" />
                  <h3 className="mb-2 text-sm font-medium">
                    No Navigation Items
                  </h3>
                  <p className="text-muted-foreground text-xs">
                    Project phases will appear here when configured.
                  </p>
                </div>
              ) : (
                <div className="space-y-1 p-4">
                  {hookData.navigationItems.map((item) => (
                    <NavigationItemComponent
                      key={item.id}
                      item={item}
                      level={0}
                      isExpanded={expandedItems.has(item.id)}
                      onToggleExpanded={() => toggleItemExpanded(item.id)}
                      onItemClick={handleItemClick}
                    />
                  ))}
                </div>
              )}
            </Suspense>
          </div>

          {/* Team Panel */}
          {config.showTeamMembers && hookData.isTeamPanelOpen && (
            <TeamPanel
              team={hookData.team}
              showWorkload={true}
              onMemberClick={handleTeamMemberClick}
            />
          )}
        </nav>
      </ErrorBoundary>
    )
  }
)

ProjectNavigation.displayName = "ProjectNavigation"

// Export all components and types
export {
  type ProjectNavigationProps,
  type ProjectInfo,
  type NavigationItem,
  type TeamMember,
  type ProjectPhase,
  type NavigationConfig,
  type ProjectProgress,
} from "./ProjectNavigationTypes"

export { useProjectNavigation } from "./useProjectNavigation"
