/**
 * Equipment Dashboard Organism
 *
 * Comprehensive electrical equipment monitoring interface combining atoms and molecules
 * to create a professional-grade equipment monitoring dashboard.
 *
 * Features:
 * - Real-time electrical equipment monitoring
 * - Professional IEEE/IEC standards compliance
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with virtualization
 * - Responsive grid layout system
 * - Advanced filtering and search capabilities
 * - Bulk operations support
 * - Alert management integration
 * - WebSocket real-time updates
 */

import React, { Suspense } from "react"

import type {
  Equipment,
  EquipmentDashboardProps,
} from "./EquipmentDashboardTypes"

import {
  Activity,
  AlertTriangle,
  Bell,
  CheckSquare,
  Download,
  Filter,
  Grid,
  List,
  MoreHorizontal,
  RefreshCw,
  Search,
  Settings,
  TrendingUp,
  Zap,
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"

import { cn } from "@/lib/utils"

import { Button } from "@/components/atoms/Button"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { AlertCard } from "@/components/molecules/AlertCard"
import { HealthIndicator } from "@/components/molecules/HealthIndicator"
import { SearchBox } from "@/components/molecules/SearchBox"
import { StatusCard } from "@/components/molecules/StatusCard"
import { Badge } from "@/components/ui_LEGACY/Badge"

import { useEquipmentDashboard } from "./useEquipmentDashboard"

// Equipment Card Component
const EquipmentCard: React.FC<{
  equipment: Equipment
  size: "sm" | "md" | "lg"
  showMetrics: boolean
  showTrends: boolean
  selected: boolean
  onSelect: (equipment: Equipment) => void
  onAction: (equipmentId: string, action: string) => void
}> = ({
  equipment,
  size,
  showMetrics,
  showTrends,
  selected,
  onSelect,
  onAction,
}) => {
  const statusMetrics = showMetrics
    ? [
        {
          label: "Voltage",
          value: equipment.measurements.voltage,
          unit: "V",
          trend: "stable" as const,
          status:
            equipment.measurements.voltage < 440 ||
            equipment.measurements.voltage > 520
              ? "warning"
              : ("normal" as const),
        },
        {
          label: "Current",
          value: equipment.measurements.current,
          unit: "A",
          trend: "stable" as const,
          status:
            equipment.measurements.current > equipment.ratedCurrent * 0.8
              ? "warning"
              : ("normal" as const),
        },
        {
          label: "Power",
          value: (equipment.measurements.power / 1000).toFixed(1),
          unit: "kW",
          trend: "up" as const,
          status: "normal" as const,
        },
        {
          label: "PF",
          value: equipment.measurements.powerFactor,
          unit: "",
          trend: "stable" as const,
          status:
            equipment.measurements.powerFactor < 0.85
              ? "warning"
              : ("normal" as const),
        },
      ]
    : []

  return (
    <div className={cn("relative", selected && "ring-primary ring-2")}>
      <StatusCard
        variant="default"
        size={size}
        title={equipment.name}
        status={
          equipment.status === "operational"
            ? "normal"
            : equipment.status === "warning"
              ? "warning"
              : equipment.status === "critical" || equipment.status === "fault"
                ? "critical"
                : "unknown"
        }
        description={`${equipment.type.replace(/_/g, " ")} • ${equipment.location.room}`}
        metrics={statusMetrics}
        lastUpdated={equipment.lastUpdate}
        showLastUpdated={size !== "sm"}
        interactive
        onClick={() => onSelect(equipment)}
        showActions={size !== "sm"}
        onActionsClick={() => onAction(equipment.id, "menu")}
        showRefresh={size === "lg"}
        onRefresh={() => onAction(equipment.id, "refresh")}
        className="h-full"
      >
        {/* Header badges */}
        <div className="mb-2 flex items-center gap-1">
          <Badge variant="outline" size="sm">
            {equipment.voltageClass}
          </Badge>
          <Badge variant="outline" size="sm">
            {equipment.priority}
          </Badge>
          {!equipment.isOnline && (
            <Badge variant="destructive" size="sm">
              Offline
            </Badge>
          )}
        </div>

        {/* Health indicator for lg size */}
        {size === "lg" && (
          <div className="mt-3">
            <HealthIndicator
              name="System Health"
              status={equipment.health}
              metrics={equipment.healthMetrics}
              variant="compact"
              showMetrics={showMetrics}
              showTrends={showTrends}
            />
          </div>
        )}
      </StatusCard>
    </div>
  )
}

// Dashboard Header Component
const DashboardHeader: React.FC<{
  title?: string
  totalCount: number
  filteredCount: number
  activeFilterCount: number
  hasUnacknowledgedAlerts: boolean
  connectionStatus: "connected" | "disconnected" | "reconnecting"
  isRefreshing: boolean
  onRefresh: () => void
  onToggleFilters: () => void
  onShowAlerts: () => void
  onShowSettings: () => void
}> = ({
  title = "Equipment Dashboard",
  totalCount,
  filteredCount,
  activeFilterCount,
  hasUnacknowledgedAlerts,
  connectionStatus,
  isRefreshing,
  onRefresh,
  onToggleFilters,
  onShowAlerts,
  onShowSettings,
}) => (
  <div className="mb-6 flex items-center justify-between">
    <div className="flex items-center gap-4">
      <div>
        <h1 className="text-foreground text-2xl font-semibold">{title}</h1>
        <div className="mt-1 flex items-center gap-4">
          <p className="text-muted-foreground text-sm">
            Showing {filteredCount} of {totalCount} equipment
          </p>
          <StatusIndicator
            variant={
              connectionStatus === "connected"
                ? "operational"
                : connectionStatus === "reconnecting"
                  ? "warning"
                  : "critical"
            }
            size="sm"
            label={
              connectionStatus === "connected"
                ? "Connected"
                : connectionStatus === "reconnecting"
                  ? "Reconnecting"
                  : "Disconnected"
            }
          />
        </div>
      </div>
    </div>

    <div className="flex items-center gap-2">
      {/* Filter toggle */}
      <Button
        variant="outline"
        size="sm"
        onClick={onToggleFilters}
        className="relative"
      >
        <Filter className="h-4 w-4" />
        Filters
        {activeFilterCount > 0 && (
          <Badge
            variant="destructive"
            size="sm"
            className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"
          >
            {activeFilterCount}
          </Badge>
        )}
      </Button>

      {/* Alerts */}
      <Button
        variant="outline"
        size="sm"
        onClick={onShowAlerts}
        className="relative"
      >
        <Bell className="h-4 w-4" />
        Alerts
        {hasUnacknowledgedAlerts && (
          <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500" />
        )}
      </Button>

      {/* Refresh */}
      <Button
        variant="outline"
        size="sm"
        onClick={onRefresh}
        disabled={isRefreshing}
      >
        <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
        Refresh
      </Button>

      {/* Settings */}
      <Button variant="outline" size="sm" onClick={onShowSettings}>
        <Settings className="h-4 w-4" />
      </Button>
    </div>
  </div>
)

// Alert Panel Component
const AlertPanel: React.FC<{
  alerts: ReadonlyArray<any>
  onAcknowledge: (alertId: string) => void
  onMarkAllRead: () => void
}> = ({ alerts, onAcknowledge, onMarkAllRead }) => {
  const criticalAlerts = alerts.filter(
    (alert) => alert.severity === "critical" && !alert.acknowledged
  )
  const warningAlerts = alerts.filter(
    (alert) => alert.severity === "warning" && !alert.acknowledged
  )

  if (alerts.length === 0) {
    return (
      <div className="text-muted-foreground py-8 text-center">
        <Activity className="mx-auto mb-3 h-12 w-12 opacity-50" />
        <p>No active alerts</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Active Alerts</h3>
        <Button variant="outline" size="sm" onClick={onMarkAllRead}>
          Mark All Read
        </Button>
      </div>

      <div className="max-h-96 space-y-2 overflow-y-auto">
        {criticalAlerts.map((alert) => (
          <AlertCard
            key={alert.id}
            variant="destructive"
            title={alert.title}
            description={alert.message}
            timestamp={alert.timestamp}
            dismissible
            onDismiss={() => onAcknowledge(alert.id)}
          />
        ))}

        {warningAlerts.map((alert) => (
          <AlertCard
            key={alert.id}
            variant="warning"
            title={alert.title}
            description={alert.message}
            timestamp={alert.timestamp}
            dismissible
            onDismiss={() => onAcknowledge(alert.id)}
          />
        ))}
      </div>
    </div>
  )
}

// Equipment Dashboard Error Fallback
const ErrorFallback: React.FC<{
  error: Error
  resetErrorBoundary: () => void
}> = ({ error, resetErrorBoundary }) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <AlertTriangle className="mb-4 h-12 w-12 text-red-500" />
    <h3 className="mb-2 text-lg font-semibold">Equipment Dashboard Error</h3>
    <p className="text-muted-foreground mb-4">
      {error.message || "An unexpected error occurred"}
    </p>
    <Button onClick={resetErrorBoundary}>Try Again</Button>
  </div>
)

// Main Equipment Dashboard Component
export const EquipmentDashboard = React.forwardRef<
  HTMLDivElement,
  EquipmentDashboardProps
>(
  (
    {
      equipment: externalEquipment,
      layout: externalLayout,
      filters: externalFilters,
      alerts: externalAlerts,
      config: externalConfig,
      loading: externalLoading = false,
      error: externalError = null,
      onEquipmentSelect,
      onEquipmentAction,
      onFiltersChange,
      onLayoutChange,
      onAlertAcknowledge,
      onRefresh,
      onConfigChange,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Use internal hook if no external data provided
    const hookData = useEquipmentDashboard({
      enableRealTime: true,
      refetchInterval: 30000,
      enableNotifications: true,
    })

    // Use external props or fall back to hook data
    const equipment = externalEquipment || hookData.filteredEquipment
    const layout = externalLayout || hookData.layout
    const filters = externalFilters || hookData.filters
    const alerts = externalAlerts || hookData.alerts
    const config = externalConfig || hookData.config
    const loading = externalLoading || hookData.loading
    const error = externalError || hookData.error

    // Use external callbacks or hook callbacks
    const handleEquipmentSelect = onEquipmentSelect || hookData.selectEquipment
    const handleEquipmentAction =
      onEquipmentAction || hookData.executeEquipmentAction
    const handleFiltersChange = onFiltersChange || hookData.updateFilters
    const handleLayoutChange = onLayoutChange || hookData.updateLayout
    const handleAlertAcknowledge =
      onAlertAcknowledge || hookData.acknowledgeAlert
    const handleRefresh = onRefresh || hookData.refreshEquipment
    const handleConfigChange = onConfigChange || hookData.updateConfig

    const [showAlerts, setShowAlerts] = React.useState(false)
    const [showSettings, setShowSettings] = React.useState(false)

    // Grid layout classes
    const gridClasses = {
      1: "grid-cols-1",
      2: "grid-cols-1 md:grid-cols-2",
      3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
    }[layout.columns]

    if (error) {
      return (
        <div
          ref={ref}
          className={cn("p-6", className)}
          data-testid={testId}
          {...props}
        >
          <ErrorFallback
            error={new Error(error)}
            resetErrorBoundary={() => handleRefresh()}
          />
        </div>
      )
    }

    return (
      <ErrorBoundary FallbackComponent={ErrorFallback} onReset={handleRefresh}>
        <div
          ref={ref}
          className={cn("space-y-6 p-6", className)}
          data-testid={testId || "equipment-dashboard"}
          {...props}
        >
          {/* Header */}
          <DashboardHeader
            totalCount={hookData.totalCount}
            filteredCount={equipment.length}
            activeFilterCount={hookData.activeFilterCount}
            hasUnacknowledgedAlerts={hookData.hasUnacknowledgedAlerts}
            connectionStatus={hookData.connectionStatus}
            isRefreshing={hookData.isRefreshing}
            onRefresh={handleRefresh}
            onToggleFilters={() => hookData.toggleFilterPanel()}
            onShowAlerts={() => setShowAlerts(true)}
            onShowSettings={() => setShowSettings(true)}
          />

          {/* Search */}
          <div className="w-full max-w-md">
            <SearchBox
              placeholder="Search equipment..."
              value={hookData.searchQuery}
              onSearch={hookData.setSearchQuery}
              loading={loading}
              className="w-full"
            />
          </div>

          {/* Alerts Panel */}
          {showAlerts && (
            <div className="bg-card rounded-lg border p-4">
              <AlertPanel
                alerts={alerts}
                onAcknowledge={handleAlertAcknowledge}
                onMarkAllRead={hookData.markAllAlertsRead}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAlerts(false)}
                className="mt-4"
              >
                Close
              </Button>
            </div>
          )}

          {/* Equipment Grid */}
          <Suspense fallback={<div>Loading equipment...</div>}>
            {loading && equipment.length === 0 ? (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div
                    key={i}
                    className="bg-muted/50 h-48 animate-pulse rounded-lg"
                  />
                ))}
              </div>
            ) : equipment.length === 0 ? (
              <div className="py-12 text-center">
                <Zap className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-medium">No Equipment Found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters or search criteria.
                </p>
              </div>
            ) : (
              <div className={cn("grid gap-4", gridClasses)}>
                {equipment.map((item) => (
                  <EquipmentCard
                    key={item.id}
                    equipment={item}
                    size={layout.cardSize}
                    showMetrics={layout.showMetrics}
                    showTrends={layout.showTrends}
                    selected={hookData.selectedEquipmentIds.includes(item.id)}
                    onSelect={handleEquipmentSelect}
                    onAction={handleEquipmentAction}
                  />
                ))}
              </div>
            )}
          </Suspense>
        </div>
      </ErrorBoundary>
    )
  }
)

EquipmentDashboard.displayName = "EquipmentDashboard"

// Export all components and types
export {
  type EquipmentDashboardProps,
  type Equipment,
  type EquipmentAlert,
  type DashboardLayout,
  type DashboardConfig,
  type EquipmentFilters,
} from "./EquipmentDashboardTypes"

export { useEquipmentDashboard } from "./useEquipmentDashboard"
