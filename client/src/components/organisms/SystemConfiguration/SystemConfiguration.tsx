/**
 * System Configuration Organism
 *
 * Comprehensive electrical system configuration interface combining atoms and molecules
 * to create a professional-grade configuration management system.
 *
 * Features:
 * - Professional electrical engineering configuration management
 * - IEEE/IEC standards compliance configuration
 * - Multi-level configuration hierarchy
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with virtualization
 * - Advanced validation and error handling
 * - Configuration presets and history
 * - Import/export functionality
 */

import React, { Suspense } from "react"

import type {
  ConfigurationCategory,
  ConfigurationField,
  ConfigurationPreset,
  ConfigurationSection,
  SystemConfigurationProps,
} from "./SystemConfigurationTypes"

import {
  AlertTriangle,
  Bell,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Clock,
  Download,
  ExternalLink,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Globe,
  History,
  Info,
  Layers,
  Lock,
  Palette,
  RotateCcw,
  Save,
  Search,
  Settings,
  Shield,
  Sliders,
  Upload,
  Wrench,
  Zap,
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"

import { cn } from "@/lib/utils"

import { But<PERSON> } from "@/components/atoms/Button"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { SearchBox } from "@/components/molecules/SearchBox"
import { Badge } from "@/components/ui_LEGACY/Badge"
import { Input } from "@/components/ui_LEGACY/Input"
import { Label } from "@/components/ui_LEGACY/Label"

import { useSystemConfiguration } from "./useSystemConfiguration"

// Configuration Field Component
const ConfigurationFieldComponent: React.FC<{
  field: ConfigurationField
  value: any
  readonly: boolean
  showHelp: boolean
  onChange: (value: any) => void
}> = ({ field, value, readonly, showHelp, onChange }) => {
  const currentValue = value !== undefined ? value : field.defaultValue
  const hasError = field.status === "error"
  const hasWarning = field.status === "warning"
  const isModified = value !== undefined && value !== field.defaultValue

  const renderFieldControl = () => {
    switch (field.type) {
      case "boolean":
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={field.id}
              checked={currentValue || false}
              onChange={(e) => onChange(e.target.checked)}
              disabled={readonly || field.isReadonly}
              className="border-input rounded"
            />
            <Label htmlFor={field.id} className="text-sm">
              {field.label}
            </Label>
          </div>
        )

      case "select":
        return (
          <div className="space-y-1">
            <Label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.isRequired && <span className="ml-1 text-red-500">*</span>}
            </Label>
            <select
              id={field.id}
              value={currentValue || ""}
              onChange={(e) => onChange(e.target.value)}
              disabled={readonly || field.isReadonly}
              className={cn(
                "border-input w-full rounded-md border px-3 py-2 text-sm",
                hasError && "border-red-500",
                hasWarning && "border-amber-500"
              )}
            >
              {!field.isRequired && <option value="">-- Select --</option>}
              {field.options?.map((option) => (
                <option
                  key={option.value.toString()}
                  value={option.value.toString()}
                  disabled={option.disabled}
                >
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )

      case "range":
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor={field.id} className="text-sm font-medium">
                {field.label}
                {field.isRequired && (
                  <span className="ml-1 text-red-500">*</span>
                )}
              </Label>
              <span className="text-muted-foreground text-sm">
                {currentValue}
                {field.unit}
              </span>
            </div>
            <input
              type="range"
              id={field.id}
              min={field.min}
              max={field.max}
              step={field.step || 1}
              value={currentValue || field.min || 0}
              onChange={(e) => onChange(parseFloat(e.target.value))}
              disabled={readonly || field.isReadonly}
              className="w-full"
            />
            <div className="text-muted-foreground flex justify-between text-xs">
              <span>
                {field.min}
                {field.unit}
              </span>
              <span>
                {field.max}
                {field.unit}
              </span>
            </div>
          </div>
        )

      case "number":
        return (
          <div className="space-y-1">
            <Label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.isRequired && <span className="ml-1 text-red-500">*</span>}
            </Label>
            <div className="relative">
              <Input
                id={field.id}
                type="number"
                value={currentValue || ""}
                onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
                placeholder={field.placeholder}
                min={field.min}
                max={field.max}
                step={field.step}
                disabled={readonly || field.isReadonly}
                className={cn(
                  hasError && "border-red-500",
                  hasWarning && "border-amber-500"
                )}
              />
              {field.unit && (
                <div className="text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm">
                  {field.unit}
                </div>
              )}
            </div>
          </div>
        )

      default:
        return (
          <div className="space-y-1">
            <Label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.isRequired && <span className="ml-1 text-red-500">*</span>}
            </Label>
            <Input
              id={field.id}
              type="text"
              value={currentValue || ""}
              onChange={(e) => onChange(e.target.value)}
              placeholder={field.placeholder}
              disabled={readonly || field.isReadonly}
              className={cn(
                hasError && "border-red-500",
                hasWarning && "border-amber-500"
              )}
            />
          </div>
        )
    }
  }

  return (
    <div className={cn("space-y-2", field.isHidden && "hidden")}>
      {renderFieldControl()}

      {/* Status indicators and messages */}
      <div className="space-y-1">
        {isModified && (
          <div className="flex items-center gap-1 text-xs text-blue-600">
            <Clock className="h-3 w-3" />
            Modified from default
          </div>
        )}

        {hasError && field.validations?.find((v) => v.severity === "error") && (
          <div className="flex items-center gap-1 text-xs text-red-600">
            <AlertTriangle className="h-3 w-3" />
            {field.validations.find((v) => v.severity === "error")?.message}
          </div>
        )}

        {hasWarning &&
          field.validations?.find((v) => v.severity === "warning") && (
            <div className="flex items-center gap-1 text-xs text-amber-600">
              <AlertTriangle className="h-3 w-3" />
              {field.validations.find((v) => v.severity === "warning")?.message}
            </div>
          )}

        {showHelp && field.helpText && (
          <div className="text-muted-foreground flex items-start gap-1 text-xs">
            <Info className="mt-0.5 h-3 w-3 flex-shrink-0" />
            <span>{field.helpText}</span>
          </div>
        )}
      </div>
    </div>
  )
}

// Configuration Section Component
const ConfigurationSectionComponent: React.FC<{
  section: ConfigurationSection
  values: Record<string, any>
  expanded: boolean
  readonly: boolean
  showAdvanced: boolean
  onFieldChange: (fieldId: string, value: any) => void
  onToggleExpanded: () => void
  onReset: () => void
}> = ({
  section,
  values,
  expanded,
  readonly,
  showAdvanced,
  onFieldChange,
  onToggleExpanded,
  onReset,
}) => {
  const IconComponent = section.icon || Settings
  const visibleFields = section.fields.filter(
    (field) => showAdvanced || !field.isHidden
  )

  return (
    <div className="rounded-lg border">
      <div
        className={cn(
          "hover:bg-muted/50 flex cursor-pointer items-center justify-between p-4",
          section.hasErrors && "border-l-4 border-l-red-500",
          section.hasWarnings &&
            !section.hasErrors &&
            "border-l-4 border-l-amber-500",
          section.hasChanges &&
            !section.hasErrors &&
            !section.hasWarnings &&
            "border-l-4 border-l-blue-500"
        )}
        onClick={onToggleExpanded}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {section.isCollapsible &&
              (expanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              ))}
            <IconComponent className="text-primary h-5 w-5" />
          </div>

          <div>
            <h3 className="font-medium">{section.title}</h3>
            {section.description && (
              <p className="text-muted-foreground text-sm">
                {section.description}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-muted-foreground text-xs">
            {section.completedFields}/{section.requiredFields}
          </div>

          {section.hasErrors && (
            <Badge variant="destructive" size="sm">
              Errors
            </Badge>
          )}

          {section.hasWarnings && !section.hasErrors && (
            <Badge variant="secondary" size="sm">
              Warnings
            </Badge>
          )}

          {section.hasChanges && (
            <Badge variant="outline" size="sm">
              Modified
            </Badge>
          )}

          {section.canReset && section.hasChanges && !readonly && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onReset()
              }}
              className="h-6 w-6 p-0"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {expanded && (
        <div className="space-y-4 border-t p-4">
          {visibleFields.map((field) => (
            <ConfigurationFieldComponent
              key={field.id}
              field={field}
              value={values[field.id]}
              readonly={readonly}
              showHelp={true}
              onChange={(value) => onFieldChange(field.id, value)}
            />
          ))}

          {visibleFields.length === 0 && (
            <div className="text-muted-foreground py-4 text-center">
              <Layers className="mx-auto mb-2 h-8 w-8 opacity-50" />
              <p className="text-sm">No fields to configure in this section</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Category Navigation Component
const CategoryNavigation: React.FC<{
  categories: ReadonlyArray<{
    category: ConfigurationCategory
    label: string
    count: number
    hasChanges: boolean
    hasErrors: boolean
  }>
  activeCategory: ConfigurationCategory
  onCategoryChange: (category: ConfigurationCategory) => void
}> = ({ categories, activeCategory, onCategoryChange }) => {
  const getCategoryIcon = (category: ConfigurationCategory) => {
    switch (category) {
      case "general":
        return Settings
      case "electrical_standards":
        return Zap
      case "safety_protocols":
        return Shield
      case "design_parameters":
        return Layers
      case "calculation_methods":
        return FileText
      case "reporting":
        return FileText
      case "compliance":
        return CheckCircle
      case "notifications":
        return Bell
      case "integrations":
        return Globe
      case "advanced":
        return Sliders
      default:
        return Settings
    }
  }

  return (
    <nav className="space-y-1">
      {categories.map(({ category, label, count, hasChanges, hasErrors }) => {
        const IconComponent = getCategoryIcon(category)
        const isActive = category === activeCategory

        return (
          <button
            key={category}
            onClick={() => onCategoryChange(category)}
            className={cn(
              "flex w-full items-center justify-between rounded-md px-3 py-2 text-left text-sm transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "hover:bg-muted",
              hasErrors && "border-l-2 border-l-red-500",
              hasChanges && !hasErrors && "border-l-2 border-l-blue-500"
            )}
          >
            <div className="flex items-center gap-2">
              <IconComponent className="h-4 w-4" />
              <span>{label}</span>
            </div>

            <div className="flex items-center gap-1">
              <Badge variant="outline" size="sm">
                {count}
              </Badge>
              {hasErrors && <AlertTriangle className="h-3 w-3 text-red-500" />}
              {hasChanges && !hasErrors && (
                <Clock className="h-3 w-3 text-blue-500" />
              )}
            </div>
          </button>
        )
      })}
    </nav>
  )
}

// Configuration Toolbar Component
const ConfigurationToolbar: React.FC<{
  hasChanges: boolean
  hasErrors: boolean
  isValidating: boolean
  isSaving: boolean
  canSave: boolean
  canReset: boolean
  onSave: () => void
  onReset: () => void
  onValidate: () => void
  onExport: () => void
  onImport: () => void
}> = ({
  hasChanges,
  hasErrors,
  isValidating,
  isSaving,
  canSave,
  canReset,
  onSave,
  onReset,
  onValidate,
  onExport,
  onImport,
}) => (
  <div className="bg-card flex items-center justify-between border-b p-4">
    <div className="flex items-center gap-4">
      <h1 className="text-xl font-semibold">System Configuration</h1>

      <div className="flex items-center gap-2">
        {hasChanges && (
          <StatusIndicator
            variant="warning"
            size="sm"
            label="Unsaved Changes"
          />
        )}

        {hasErrors && (
          <StatusIndicator
            variant="critical"
            size="sm"
            label="Configuration Errors"
          />
        )}
      </div>
    </div>

    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={onValidate}
        disabled={isValidating}
      >
        <CheckCircle
          className={cn("mr-1 h-4 w-4", isValidating && "animate-spin")}
        />
        Validate
      </Button>

      <Button variant="outline" size="sm" onClick={onExport}>
        <Download className="mr-1 h-4 w-4" />
        Export
      </Button>

      <Button variant="outline" size="sm" onClick={onImport}>
        <Upload className="mr-1 h-4 w-4" />
        Import
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={onReset}
        disabled={!canReset}
      >
        <RotateCcw className="mr-1 h-4 w-4" />
        Reset
      </Button>

      <Button onClick={onSave} disabled={!canSave} className="relative">
        <Save className={cn("mr-1 h-4 w-4", isSaving && "animate-pulse")} />
        {isSaving ? "Saving..." : "Save Changes"}
      </Button>
    </div>
  </div>
)

// Error Fallback Component
const ErrorFallback: React.FC<{
  error: Error
  resetErrorBoundary: () => void
}> = ({ error, resetErrorBoundary }) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <AlertTriangle className="mb-4 h-12 w-12 text-red-500" />
    <h3 className="mb-2 text-lg font-semibold">Configuration Error</h3>
    <p className="text-muted-foreground mb-4">
      {error.message || "An unexpected error occurred"}
    </p>
    <Button onClick={resetErrorBoundary}>Try Again</Button>
  </div>
)

// Main System Configuration Component
export const SystemConfiguration = React.forwardRef<
  HTMLDivElement,
  SystemConfigurationProps
>(
  (
    {
      sections: externalSections,
      presets: externalPresets,
      history: externalHistory,
      filters: externalFilters,
      activeCategory: externalActiveCategory,
      loading: externalLoading = false,
      error: externalError = null,
      readonly = false,
      showAdvanced: externalShowAdvanced = false,
      onFieldChange,
      onSectionToggle,
      onCategoryChange,
      onApplyPreset,
      onSave,
      onReset,
      onExport,
      onImport,
      onValidate,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Use internal hook if no external data provided
    const hookData = useSystemConfiguration({
      enableRealTime: true,
      enableAutoValidation: true,
      showAdvanced: externalShowAdvanced,
    })

    // Use external props or fall back to hook data
    const sections = externalSections || hookData.sections
    const presets = externalPresets || hookData.presets
    const history = externalHistory || hookData.history
    const filters = externalFilters || hookData.filters
    const activeCategory = externalActiveCategory || hookData.activeCategory
    const loading = externalLoading || hookData.loading
    const error = externalError || hookData.error
    const showAdvanced = externalShowAdvanced || hookData.showAdvanced

    // Use external callbacks or hook callbacks
    const handleFieldChange = onFieldChange || hookData.updateField
    const handleSectionToggle = onSectionToggle || hookData.toggleSection
    const handleCategoryChange = onCategoryChange || hookData.setActiveCategory
    const handleApplyPreset = onApplyPreset || hookData.applyPreset
    const handleSave = onSave || hookData.saveConfiguration
    const handleReset = onReset || hookData.resetConfiguration
    const handleExport = onExport || hookData.exportConfiguration
    const handleImport = onImport || (() => {})
    const handleValidate = onValidate || hookData.validateConfiguration

    const [searchQuery, setSearchQuery] = React.useState("")

    if (error) {
      return (
        <div
          ref={ref}
          className={cn("p-6", className)}
          data-testid={testId}
          {...props}
        >
          <ErrorFallback
            error={new Error(error)}
            resetErrorBoundary={() => hookData.refreshConfiguration()}
          />
        </div>
      )
    }

    return (
      <ErrorBoundary
        FallbackComponent={ErrorFallback}
        onReset={hookData.refreshConfiguration}
      >
        <div
          ref={ref}
          className={cn("bg-background flex h-full flex-col", className)}
          data-testid={testId || "system-configuration"}
          {...props}
        >
          {/* Toolbar */}
          <ConfigurationToolbar
            hasChanges={hookData.hasUnsavedChanges}
            hasErrors={hookData.validationSummary.errorFields > 0}
            isValidating={hookData.isValidating}
            isSaving={hookData.isSaving}
            canSave={hookData.canSave}
            canReset={hookData.canReset}
            onSave={handleSave}
            onReset={() => handleReset()}
            onValidate={handleValidate}
            onExport={() => handleExport()}
            onImport={handleImport}
          />

          <div className="flex flex-1 overflow-hidden">
            {/* Sidebar */}
            <div className="bg-card/50 w-64 border-r p-4">
              <div className="space-y-4">
                {/* Search */}
                <SearchBox
                  placeholder="Search configuration..."
                  value={searchQuery}
                  onSearch={setSearchQuery}
                  className="w-full"
                />

                {/* Category Navigation */}
                <div>
                  <h3 className="mb-2 text-sm font-medium">Categories</h3>
                  <CategoryNavigation
                    categories={hookData.categorySummary}
                    activeCategory={activeCategory}
                    onCategoryChange={handleCategoryChange}
                  />
                </div>

                {/* Advanced Toggle */}
                <div className="flex items-center space-x-2 border-t pt-2">
                  <input
                    type="checkbox"
                    id="show-advanced"
                    checked={showAdvanced}
                    onChange={() => hookData.toggleAdvancedMode()}
                    className="rounded"
                  />
                  <Label htmlFor="show-advanced" className="text-sm">
                    Show advanced options
                  </Label>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <Suspense fallback={<div>Loading configuration...</div>}>
                {loading && hookData.filteredSections.length === 0 ? (
                  <div className="space-y-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="space-y-3 rounded-lg border p-4">
                        <div className="bg-muted h-6 animate-pulse rounded" />
                        <div className="space-y-2">
                          {Array.from({ length: 3 }).map((_, j) => (
                            <div
                              key={j}
                              className="bg-muted/50 h-10 animate-pulse rounded"
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : hookData.filteredSections.length === 0 ? (
                  <div className="py-12 text-center">
                    <Settings className="text-muted-foreground mx-auto mb-4 h-12 w-12 opacity-50" />
                    <h3 className="mb-2 text-lg font-medium">
                      No Configuration Sections
                    </h3>
                    <p className="text-muted-foreground">
                      No configuration sections match your current filters.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {hookData.filteredSections.map((section) => (
                      <ConfigurationSectionComponent
                        key={section.id}
                        section={section}
                        values={hookData.selectedFields.reduce(
                          (acc, fieldId) => {
                            // This would normally come from the store
                            acc[fieldId] = undefined // Placeholder
                            return acc
                          },
                          {} as Record<string, any>
                        )}
                        expanded={hookData.expandedSections.has(section.id)}
                        readonly={readonly}
                        showAdvanced={showAdvanced}
                        onFieldChange={handleFieldChange}
                        onToggleExpanded={() => handleSectionToggle(section.id)}
                        onReset={() => hookData.resetSection(section.id)}
                      />
                    ))}
                  </div>
                )}
              </Suspense>
            </div>
          </div>
        </div>
      </ErrorBoundary>
    )
  }
)

SystemConfiguration.displayName = "SystemConfiguration"

// Export all components and types
export {
  type SystemConfigurationProps,
  type ConfigurationSection,
  type ConfigurationField,
  type ConfigurationCategory,
  type ConfigurationPreset,
  type ConfigurationValidationResult,
} from "./SystemConfigurationTypes"

export { useSystemConfiguration } from "./useSystemConfiguration"
