/**
 * Integration tests for TaskForm component
 * Tests React Query integration, form validation, and mutation handling
 */

import React from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import type { TaskRead, UserRead } from "../../../types/api"

import { TaskForm } from "../TaskForm"

// Using imported types from API types

// Mock the task API
const mockTaskApi = {
  create: vi.fn(),
  update: vi.fn(),
  list: vi.fn(),
  getById: vi.fn(),
  delete: vi.fn(),
}

// Mock the user API
const mockUserApi = {
  list: vi.fn(),
  getById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
}

vi.mock("@/api/tasks", () => ({
  taskApi: mockTaskApi,
}))

vi.mock("@/api/users", () => ({
  userApi: mockUserApi,
}))

// Mock data
const mockUsers: UserRead[] = [
  {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    is_superuser: false,
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "Jane Smith",
    email: "<EMAIL>",
    is_superuser: false,
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
]

const mockTask: TaskRead = {
  id: 1,
  task_id: "task-1",
  project_id: 1,
  title: "Existing Task",
  description: "Existing task description",
  priority: "High",
  status: "In Progress",
  due_date: new Date("2024-12-31"),
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  assignments: [
    {
      id: 1,
      user_id: 1,
      task_id: 1,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      assigned_at: new Date("2024-01-01T00:00:00Z"),
    },
  ],
}

const mockCreatedTask: TaskRead = {
  id: 2,
  task_id: "task-new",
  project_id: 1,
  title: "New Task",
  description: "New task description",
  priority: "Medium",
  status: "Not Started",
  due_date: null,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  assignments: [],
}

// Test utilities
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <QueryClientProvider client={createTestQueryClient()}>
    {children}
  </QueryClientProvider>
)

describe("TaskForm Integration Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUserApi.list.mockResolvedValue({
      data: mockUsers,
      status: 200,
    })
    mockTaskApi.create.mockResolvedValue({
      data: mockCreatedTask,
      status: 201,
    })
    mockTaskApi.update.mockResolvedValue({
      data: { ...mockTask, title: "Updated Task" },
      status: 200,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Create Task Flow", () => {
    it("renders create form and loads users", async () => {
      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Check form elements
      expect(screen.getByLabelText(/title/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/priority/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/status/i)).toBeInTheDocument()

      // Wait for users to load
      await waitFor(() => {
        expect(mockUserApi.list).toHaveBeenCalled()
      })

      // Check form title
      expect(screen.getByText("Create New Task")).toBeInTheDocument()
    })

    it("successfully creates a new task", async () => {
      const user = userEvent.setup()
      const onSuccess = vi.fn()

      render(
        <TestWrapper>
          <TaskForm projectId={1} onSuccess={onSuccess} />
        </TestWrapper>
      )

      // Fill out form
      await user.type(screen.getByLabelText(/title/i), "New Task")
      await user.type(
        screen.getByLabelText(/description/i),
        "New task description"
      )
      await user.selectOptions(screen.getByLabelText(/priority/i), "Medium")
      await user.selectOptions(screen.getByLabelText(/status/i), "Not Started")

      // Submit form
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Wait for submission
      await waitFor(() => {
        expect(mockTaskApi.create).toHaveBeenCalledWith({
          projectId: 1,
          data: expect.objectContaining({
            project_id: 1,
            title: "New Task",
            description: "New task description",
            priority: "Medium",
            status: "Not Started",
          }),
        })
      })

      // Check success callback
      expect(onSuccess).toHaveBeenCalledWith(mockCreatedTask)
    })

    it("displays validation errors for invalid input", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Try to submit empty form
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Check validation errors
      await waitFor(() => {
        expect(screen.getByText(/title is required/i)).toBeInTheDocument()
      })

      // API should not be called
      expect(mockTaskApi.create).not.toHaveBeenCalled()
    })

    it("handles API errors during creation", async () => {
      const user = userEvent.setup()
      mockTaskApi.create.mockRejectedValue(new Error("API Error"))

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Fill out form
      await user.type(screen.getByLabelText(/title/i), "New Task")

      // Submit form
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Wait for error
      await waitFor(() => {
        expect(screen.getByText(/error creating task/i)).toBeInTheDocument()
      })
    })
  })

  describe("Edit Task Flow", () => {
    it("renders edit form with existing task data", async () => {
      render(
        <TestWrapper>
          <TaskForm projectId={1} task={mockTask} />
        </TestWrapper>
      )

      // Check form is populated
      expect(screen.getByDisplayValue("Existing Task")).toBeInTheDocument()
      expect(
        screen.getByDisplayValue("Existing task description")
      ).toBeInTheDocument()
      expect(screen.getByDisplayValue("High")).toBeInTheDocument()
      expect(screen.getByDisplayValue("In Progress")).toBeInTheDocument()

      // Check form title
      expect(screen.getByText("Edit Task")).toBeInTheDocument()
    })

    it("successfully updates an existing task", async () => {
      const user = userEvent.setup()
      const onSuccess = vi.fn()

      render(
        <TestWrapper>
          <TaskForm projectId={1} task={mockTask} onSuccess={onSuccess} />
        </TestWrapper>
      )

      // Update title
      const titleInput = screen.getByDisplayValue("Existing Task")
      await user.clear(titleInput)
      await user.type(titleInput, "Updated Task")

      // Submit form
      const submitButton = screen.getByRole("button", { name: /update task/i })
      await user.click(submitButton)

      // Wait for submission
      await waitFor(() => {
        expect(mockTaskApi.update).toHaveBeenCalledWith({
          projectId: 1,
          taskId: "task-1",
          data: expect.objectContaining({
            title: "Updated Task",
          }),
        })
      })

      // Check success callback
      expect(onSuccess).toHaveBeenCalled()
    })
  })

  describe("User Assignment", () => {
    it("allows selecting and deselecting users", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Wait for users to load
      await waitFor(() => {
        expect(screen.getByText("John Doe")).toBeInTheDocument()
      })

      // Select a user
      const userCheckbox = screen.getByLabelText("John Doe")
      await user.click(userCheckbox)

      expect(userCheckbox).toBeChecked()

      // Deselect the user
      await user.click(userCheckbox)
      expect(userCheckbox).not.toBeChecked()
    })

    it("includes assigned users in form submission", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Fill required fields
      await user.type(screen.getByLabelText(/title/i), "Task with Assignment")

      // Wait for users to load and select one
      await waitFor(() => {
        expect(screen.getByText("John Doe")).toBeInTheDocument()
      })

      const userCheckbox = screen.getByLabelText("John Doe")
      await user.click(userCheckbox)

      // Submit form
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Check API call includes assigned users
      await waitFor(() => {
        expect(mockTaskApi.create).toHaveBeenCalledWith({
          projectId: 1,
          data: expect.objectContaining({
            assigned_user_ids: [1],
          }),
        })
      })
    })
  })

  describe("Form State Management", () => {
    it("shows loading state during submission", async () => {
      const user = userEvent.setup()

      // Mock slow API response
      mockTaskApi.create.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () => resolve({ data: mockCreatedTask, status: 201 }),
              100
            )
          )
      )

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Fill and submit form
      await user.type(screen.getByLabelText(/title/i), "New Task")
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Check loading state
      expect(
        screen.getByRole("button", { name: /creating/i })
      ).toBeInTheDocument()
      expect(submitButton).toBeDisabled()

      // Wait for completion
      await waitFor(() => {
        expect(
          screen.queryByRole("button", { name: /creating/i })
        ).not.toBeInTheDocument()
      })
    })

    it("handles cancel action", async () => {
      const user = userEvent.setup()
      const onCancel = vi.fn()

      render(
        <TestWrapper>
          <TaskForm projectId={1} onCancel={onCancel} />
        </TestWrapper>
      )

      // Click cancel button
      const cancelButton = screen.getByRole("button", { name: /cancel/i })
      await user.click(cancelButton)

      expect(onCancel).toHaveBeenCalled()
    })
  })

  describe("Date Handling", () => {
    it("handles due date selection", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskForm projectId={1} />
        </TestWrapper>
      )

      // Select due date
      const dateInput = screen.getByLabelText(/due date/i)
      await user.type(dateInput, "2024-12-31")

      // Fill required fields and submit
      await user.type(screen.getByLabelText(/title/i), "Task with Due Date")
      const submitButton = screen.getByRole("button", { name: /create task/i })
      await user.click(submitButton)

      // Check API call includes due date
      await waitFor(() => {
        expect(mockTaskApi.create).toHaveBeenCalledWith({
          projectId: 1,
          data: expect.objectContaining({
            due_date: expect.any(Date),
          }),
        })
      })
    })
  })
})
