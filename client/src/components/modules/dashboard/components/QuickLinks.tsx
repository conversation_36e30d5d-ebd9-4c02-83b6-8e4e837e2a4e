import React from "react"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"@/components/atoms/card

interface QuickLinksProps {
  onLinkClick?: () => void
}

const QuickLinks: React.FC<QuickLinksProps> = ({ onLinkClick }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Links</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-2">
        <Button variant="outline" onClick={onLinkClick}>
          Scenario 1
        </Button>
        <Button variant="outline" onClick={onLinkClick}>
          Scenario 2
        </Button>
      </CardContent>
    </Card>
  )
}

export default QuickLinks
