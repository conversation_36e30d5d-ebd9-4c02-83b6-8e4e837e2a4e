/**
 * Unified Badge System - UI Primitives Layer
 * Consolidates ComponentBadge, StatusBadge, PriorityBadge into a single unified system
 *
 * Features:
 * - Unified badge variants with consistent styling
 * - Multiple badge types (status, priority, component, generic)
 * - Comprehensive accessibility support (WCAG 2.1 AA)
 * - TypeScript strict mode compliance
 * - Performance optimized with CVA
 * - 100% backward compatibility
 */

import React from "react"

import type { VariantProps } from "class-variance-authority"

import { cva } from "class-variance-authority"
import {
  AlertCircle,
  AlertTriangle,
  Archive,
  ArrowDown,
  ArrowUp,
  CheckCircle,
  FileText,
  Minus,
  Pause,
  Play,
  XCircle,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui_LEGACY/badge"

// Unified badge variants using CVA for consistent styling
export const unifiedBadgeVariants = cva(
  "inline-flex items-center gap-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      size: {
        sm: "px-1.5 py-0.5 text-xs",
        md: "px-2 py-1 text-sm",
        lg: "px-3 py-1.5 text-base",
      },
      variant: {
        default: "border bg-background",
        solid: "border-transparent text-white",
        outline: "bg-transparent border-2",
        ghost: "border-transparent bg-transparent",
      },
      intent: {
        // Status intents
        active:
          "bg-green-100 text-green-800 border-green-200 hover:bg-green-200",
        inactive: "bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200",
        preferred:
          "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",
        available: "bg-green-100 text-green-700 border-green-200",
        limited: "bg-yellow-100 text-yellow-700 border-yellow-200",
        out_of_stock: "bg-red-100 text-red-700 border-red-200",
        discontinued: "bg-gray-100 text-gray-500 border-gray-200",
        on_order: "bg-blue-100 text-blue-700 border-blue-200",

        // Project status intents
        draft: "bg-gray-100 text-gray-600 border-gray-200",
        paused: "bg-yellow-100 text-yellow-700 border-yellow-200",
        completed: "bg-green-100 text-green-700 border-green-200",
        cancelled: "bg-red-100 text-red-700 border-red-200",

        // Priority intents
        low: "bg-blue-50 text-blue-700 border-blue-200",
        medium: "bg-gray-50 text-gray-700 border-gray-200",
        high: "bg-orange-50 text-orange-700 border-orange-200",
        critical: "bg-red-50 text-red-700 border-red-200",

        // Generic semantic intents
        info: "bg-blue-100 text-blue-800 border-blue-200",
        success: "bg-green-100 text-green-800 border-green-200",
        warning: "bg-yellow-100 text-yellow-800 border-yellow-200",
        error: "bg-red-100 text-red-800 border-red-200",

        // Electrical status intents
        energized: "bg-green-100 text-green-800 border-green-200",
        deenergized: "bg-gray-100 text-gray-600 border-gray-200",
        fault: "bg-red-100 text-red-800 border-red-200",
        overload: "bg-orange-100 text-orange-800 border-orange-200",
        undervoltage: "bg-yellow-100 text-yellow-800 border-yellow-200",
        normal: "bg-green-100 text-green-800 border-green-200",
        alarm: "bg-red-100 text-red-800 border-red-200",
        maintenance: "bg-blue-100 text-blue-800 border-blue-200",
        calibration: "bg-purple-100 text-purple-800 border-purple-200",
        testing: "bg-cyan-100 text-cyan-800 border-cyan-200",
        commissioned: "bg-green-100 text-green-800 border-green-200",
        decommissioned: "bg-gray-100 text-gray-600 border-gray-200",
      },
    },
    compoundVariants: [
      // Solid variant overrides for better contrast
      {
        variant: "solid",
        intent: ["active", "preferred", "completed", "success"],
        class: "bg-green-600 text-white border-green-600 hover:bg-green-700",
      },
      {
        variant: "solid",
        intent: ["error", "critical", "cancelled", "out_of_stock"],
        class: "bg-red-600 text-white border-red-600 hover:bg-red-700",
      },
      {
        variant: "solid",
        intent: ["warning", "high", "limited", "paused"],
        class: "bg-yellow-600 text-white border-yellow-600 hover:bg-yellow-700",
      },
      {
        variant: "solid",
        intent: ["info", "low", "on_order"],
        class: "bg-blue-600 text-white border-blue-600 hover:bg-blue-700",
      },
    ],
    defaultVariants: {
      size: "md",
      variant: "default",
      intent: "active",
    },
  }
)

// Configuration for different badge types
const badgeTypeConfig = {
  // Component status configuration
  component: {
    active: { label: "Active", icon: CheckCircle },
    inactive: { label: "Inactive", icon: XCircle },
    preferred: { label: "Preferred", icon: AlertTriangle },
    available: { label: "Available", icon: CheckCircle },
    limited: { label: "Limited Stock", icon: AlertTriangle },
    out_of_stock: { label: "Out of Stock", icon: XCircle },
    discontinued: { label: "Discontinued", icon: Archive },
    on_order: { label: "On Order", icon: FileText },
  },

  // Project status configuration
  status: {
    draft: { label: "Draft", icon: FileText },
    active: { label: "Active", icon: Play },
    paused: { label: "Paused", icon: Pause },
    completed: { label: "Completed", icon: CheckCircle },
    cancelled: { label: "Cancelled", icon: XCircle },
  },

  // Priority configuration
  priority: {
    low: { label: "Low", icon: ArrowDown },
    medium: { label: "Medium", icon: Minus },
    high: { label: "High", icon: ArrowUp },
    critical: { label: "Critical", icon: AlertTriangle },
  },

  // Generic semantic configuration
  generic: {
    info: { label: "Info", icon: AlertCircle },
    success: { label: "Success", icon: CheckCircle },
    warning: { label: "Warning", icon: AlertTriangle },
    error: { label: "Error", icon: XCircle },
  },

  // Electrical status configuration
  electrical: {
    energized: { label: "Energized", icon: CheckCircle },
    deenergized: { label: "De-energized", icon: XCircle },
    fault: { label: "Fault", icon: XCircle },
    overload: { label: "Overload", icon: AlertTriangle },
    undervoltage: { label: "Undervoltage", icon: AlertTriangle },
    normal: { label: "Normal", icon: CheckCircle },
    alarm: { label: "Alarm", icon: AlertCircle },
    maintenance: { label: "Maintenance", icon: AlertCircle },
    calibration: { label: "Calibration", icon: AlertCircle },
    testing: { label: "Testing", icon: AlertCircle },
    commissioned: { label: "Commissioned", icon: CheckCircle },
    decommissioned: { label: "Decommissioned", icon: Archive },
  },
} as const

// Type definitions
export type BadgeIntent =
  | keyof typeof badgeTypeConfig.component
  | keyof typeof badgeTypeConfig.status
  | keyof typeof badgeTypeConfig.priority
  | keyof typeof badgeTypeConfig.generic
  | keyof typeof badgeTypeConfig.electrical

export type BadgeType = keyof typeof badgeTypeConfig

export interface UnifiedBadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof unifiedBadgeVariants> {
  type?: BadgeType
  intent: BadgeIntent
  showIcon?: boolean
  showLabel?: boolean
  customLabel?: string
  customIcon?: React.ComponentType<{ className?: string }>
  pulse?: boolean
  interactive?: boolean
  "data-testid"?: string
}

export const UnifiedBadge = React.forwardRef<
  HTMLSpanElement,
  UnifiedBadgeProps
>(
  (
    {
      type = "generic",
      intent,
      size,
      variant,
      showIcon = true,
      showLabel = true,
      customLabel,
      customIcon: CustomIcon,
      pulse = false,
      interactive = false,
      className,
      role = "status",
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Get configuration based on type and intent
    const typeConfig = badgeTypeConfig[type] as Record<
      string,
      { label: string; icon: any }
    >
    const config = typeConfig[intent as keyof typeof typeConfig]

    if (!config) {
      console.warn(
        `UnifiedBadge: Invalid intent "${intent}" for type "${type}". Falling back to generic.`
      )
      const fallbackConfig =
        badgeTypeConfig.generic[
          intent as keyof typeof badgeTypeConfig.generic
        ] || badgeTypeConfig.generic.info
      const Icon = CustomIcon || fallbackConfig.icon
      const label = customLabel || fallbackConfig.label

      return (
        <Badge
          ref={ref}
          className={cn(
            unifiedBadgeVariants({ intent: "info", size, variant }),
            pulse && "animate-pulse",
            interactive && "cursor-pointer hover:opacity-80",
            className
          )}
          role={role}
          aria-label={`${type} ${intent}: ${label}`}
          data-testid={testId || `unified-badge-${type}-${intent}`}
          {...props}
        >
          {showIcon && Icon && <Icon className="h-3 w-3" aria-hidden="true" />}
          {showLabel && <span>{label}</span>}
        </Badge>
      )
    }

    const Icon = CustomIcon || config.icon
    const label = customLabel || config.label

    return (
      <Badge
        ref={ref}
        className={cn(
          unifiedBadgeVariants({ intent: intent as any, size, variant }),
          pulse && "animate-pulse",
          interactive &&
            "focus:ring-ring cursor-pointer hover:opacity-80 focus:ring-2 focus:ring-offset-2",
          className
        )}
        role={role}
        aria-label={`${type} ${intent}: ${label}`}
        data-testid={testId || `unified-badge-${type}-${intent}`}
        tabIndex={interactive ? 0 : undefined}
        {...props}
      >
        {showIcon && Icon && <Icon className="h-3 w-3" aria-hidden="true" />}
        {showLabel && <span>{label}</span>}
      </Badge>
    )
  }
)

UnifiedBadge.displayName = "UnifiedBadge"

// Convenience components for backward compatibility and ease of use

// Component Badge convenience components
export const ComponentBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<UnifiedBadgeProps, "type" | "intent"> & {
    status: keyof typeof badgeTypeConfig.component
  }
>(({ status, ...props }, ref) => (
  <UnifiedBadge ref={ref} type="component" intent={status} {...props} />
))
ComponentBadge.displayName = "ComponentBadge"

// Status Badge convenience components
export const StatusBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<UnifiedBadgeProps, "type" | "intent"> & {
    status: keyof typeof badgeTypeConfig.status
  }
>(({ status, ...props }, ref) => (
  <UnifiedBadge ref={ref} type="status" intent={status} {...props} />
))
StatusBadge.displayName = "StatusBadge"

// Priority Badge convenience components
export const PriorityBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<UnifiedBadgeProps, "type" | "intent"> & {
    priority: keyof typeof badgeTypeConfig.priority
  }
>(({ priority, ...props }, ref) => (
  <UnifiedBadge ref={ref} type="priority" intent={priority} {...props} />
))
PriorityBadge.displayName = "PriorityBadge"

// Electrical Badge convenience components
export const ElectricalBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<UnifiedBadgeProps, "type" | "intent"> & {
    status: keyof typeof badgeTypeConfig.electrical
  }
>(({ status, ...props }, ref) => (
  <UnifiedBadge ref={ref} type="electrical" intent={status} {...props} />
))
ElectricalBadge.displayName = "ElectricalBadge"

// Individual convenience components for common use cases
export const ActiveBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="component" intent="active" {...props} />

export const InactiveBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="component" intent="inactive" {...props} />

export const PreferredBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="component" intent="preferred" {...props} />

export const DraftBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="status" intent="draft" {...props} />

export const CompletedBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="status" intent="completed" {...props} />

export const HighPriorityBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="priority" intent="high" {...props} />

export const CriticalPriorityBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="priority" intent="critical" {...props} />

// Electrical convenience components
export const EnergizedBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="electrical" intent="energized" {...props} />

export const DeenergizedBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="electrical" intent="deenergized" {...props} />

export const FaultBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="electrical" intent="fault" {...props} />

export const NormalBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="electrical" intent="normal" {...props} />

export const MaintenanceBadge = (
  props: Omit<UnifiedBadgeProps, "type" | "intent">
) => <UnifiedBadge type="electrical" intent="maintenance" {...props} />

// Utility hooks and functions

// Hook for getting badge props from component data (backward compatibility)
export const useComponentBadgeProps = (component: {
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
  component_type?: string
  category?: string
  component_type_id?: number
  category_id?: number
}): { type: "component"; intent: keyof typeof badgeTypeConfig.component } => {
  if (!component.is_active) {
    return { type: "component", intent: "inactive" }
  }

  if (component.is_preferred) {
    return { type: "component", intent: "preferred" }
  }

  // Map stock status to badge intent
  const stockStatusMap: Record<string, keyof typeof badgeTypeConfig.component> =
    {
      available: "available",
      limited: "limited",
      out_of_stock: "out_of_stock",
      discontinued: "discontinued",
      on_order: "on_order",
    }

  const badgeIntent = component.stock_status
    ? stockStatusMap[component.stock_status] || "active"
    : "active"

  return { type: "component", intent: badgeIntent }
}

// Hook for getting status badge props (backward compatibility)
export const useStatusBadgeProps = (
  status: string
): {
  type: "status"
  intent: keyof typeof badgeTypeConfig.status
} => {
  const normalizedStatus =
    status.toLowerCase() as keyof typeof badgeTypeConfig.status
  const validStatuses = Object.keys(badgeTypeConfig.status)

  if (validStatuses.includes(normalizedStatus)) {
    return { type: "status", intent: normalizedStatus }
  }

  return { type: "status", intent: "draft" }
}

// Hook for getting priority badge props (backward compatibility)
export const usePriorityBadgeProps = (
  priority: string
): {
  type: "priority"
  intent: keyof typeof badgeTypeConfig.priority
} => {
  const normalizedPriority =
    priority.toLowerCase() as keyof typeof badgeTypeConfig.priority
  const validPriorities = Object.keys(badgeTypeConfig.priority)

  if (validPriorities.includes(normalizedPriority)) {
    return { type: "priority", intent: normalizedPriority }
  }

  return { type: "priority", intent: "medium" }
}

// Hook for getting electrical badge props (new electrical functionality)
export const useElectricalBadgeProps = (
  status: string
): {
  type: "electrical"
  intent: keyof typeof badgeTypeConfig.electrical
} => {
  const normalizedStatus =
    status.toLowerCase() as keyof typeof badgeTypeConfig.electrical
  const validStatuses = Object.keys(badgeTypeConfig.electrical)

  if (validStatuses.includes(normalizedStatus)) {
    return { type: "electrical", intent: normalizedStatus }
  }

  return { type: "electrical", intent: "normal" }
}

// Type guards
export const isValidComponentStatus = (
  status: string
): status is keyof typeof badgeTypeConfig.component => {
  return Object.keys(badgeTypeConfig.component).includes(status)
}

export const isValidProjectStatus = (
  status: string
): status is keyof typeof badgeTypeConfig.status => {
  return Object.keys(badgeTypeConfig.status).includes(status)
}

export const isValidProjectPriority = (
  priority: string
): priority is keyof typeof badgeTypeConfig.priority => {
  return Object.keys(badgeTypeConfig.priority).includes(priority)
}

export const isValidElectricalStatus = (
  status: string
): status is keyof typeof badgeTypeConfig.electrical => {
  return Object.keys(badgeTypeConfig.electrical).includes(status)
}

// Helper functions
export const getBadgeVariantFromStatus = (status: string) => {
  const config =
    badgeTypeConfig.component[
      status as keyof typeof badgeTypeConfig.component
    ] ||
    badgeTypeConfig.status[status as keyof typeof badgeTypeConfig.status] ||
    badgeTypeConfig.priority[status as keyof typeof badgeTypeConfig.priority] ||
    badgeTypeConfig.generic.info

  return config
}

export const getPriorityLevel = (
  priority: keyof typeof badgeTypeConfig.priority
): number => {
  const levels = { low: 1, medium: 2, high: 3, critical: 4 }
  return levels[priority]
}

// Export types for external use
export type UnifiedBadgeSize = NonNullable<UnifiedBadgeProps["size"]>
export type UnifiedBadgeVariant = NonNullable<UnifiedBadgeProps["variant"]>
export type ComponentBadgeStatus = keyof typeof badgeTypeConfig.component
export type ProjectStatus = keyof typeof badgeTypeConfig.status
export type ProjectPriority = keyof typeof badgeTypeConfig.priority
export type ElectricalStatus = keyof typeof badgeTypeConfig.electrical

// Export configuration for external use
export { badgeTypeConfig }
