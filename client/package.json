{"name": "ued-dev", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write --log-level=warn \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "vitest --run src/", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage --run", "test:coverage:watch": "vitest run --coverage --watch", "test:ui": "vitest --ui", "test:e2e": "playwright test tests/e2e", "test:e2e:ui": "playwright test --ui tests/e2e"}, "dependencies": {"@headless-tree/core": "^1.2.1", "@hookform/resolvers": "^5.2.0", "@origin-space/image-cropper": "^0.1.9", "@radix-ui/react-icons": "^1.3.2", "@remixicon/react": "^4.6.0", "@tabler/icons-react": "^3.34.1", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-query-persist-client": "^5.83.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "idb": "^8.0.3", "immer": "^10.1.1", "input-otp": "^1.4.2", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.4.4", "next-themes": "^0.4.6", "postcss": "^8.5.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-aria-components": "^1.11.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-resizable-panels": "^3.0.3", "shadcn-ui": "^0.9.5", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.31.0", "@ianvs/prettier-plugin-sort-imports": "^4.5.1", "@next/eslint-plugin-next": "^15.4.4", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "3.2.4", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "husky": "^9.1.7", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "msw": "^2.10.4", "playwright": "^1.54.1", "prettier": "3.6.2", "prettier-oxc-parser": "^0.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vitest": "^3.2.4"}, "prettier": {"endOfLine": "lf", "semi": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "importOrder": ["^react$", "^next$", "", "<TYPES>^(node:)", "<TYPES>", "<TYPES>^[.]", "", "<BUILTIN_MODULES>", "<THIRD_PARTY_MODULES>", "", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/styles/(.*)$", "", "^@/app/(.*)$", "", "^[./]", "^(?!.*[.]css$)[./].*$", ".css$"], "overrides": [{"files": "**/*.test.ts", "options": {"importOrder": ["^vitest", "<THIRD_PARTY_MODULES>", "^[.]"]}}], "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "plugins": ["prettier-oxc-parser", "@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrderTypeScriptVersion": "5.0.0", "importOrderCaseSensitive": false}}